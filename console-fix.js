// XPath测试器 - 控制台快速修复脚本
// 在浏览器控制台中运行此代码来修复初始化问题

(function() {
    console.log('🔧 XPath测试器快速修复脚本');
    
    // 检查当前状态
    const currentStatus = {
        sidebar: !!document.getElementById('xpath-sidebar'),
        initialized: !!window.xpathSidebarInitialized,
        hasToggle: typeof window.toggleSidebar === 'function',
        hasInstance: !!window.xpathSidebarInstance,
        hasInitFunc: typeof window.initializeXPathSidebar === 'function'
    };
    
    console.log('📊 当前状态:', currentStatus);
    
    // 如果函数存在但未初始化，进行修复
    if (currentStatus.hasToggle && !currentStatus.initialized) {
        console.log('🔄 检测到初始化问题，正在修复...');
        
        try {
            // 调用初始化函数
            if (currentStatus.hasInitFunc) {
                window.initializeXPathSidebar();
                console.log('✅ 调用了初始化函数');
            }
            
            // 手动设置标志
            window.xpathSidebarInitialized = true;
            console.log('✅ 设置初始化标志');
            
            // 创建实例对象
            if (!window.xpathSidebarInstance) {
                window.xpathSidebarInstance = {
                    toggleSidebar: window.toggleSidebar,
                    showSidebar: window.showSidebar,
                    hideSidebar: window.hideSidebar,
                    sidebar: document.getElementById('xpath-sidebar'),
                    sidebarVisible: false
                };
                console.log('✅ 创建实例对象');
            }
            
            console.log('🎉 修复完成！现在可以使用以下命令：');
            console.log('   window.toggleSidebar() - 切换侧边栏');
            console.log('   window.showSidebar() - 显示侧边栏');
            console.log('   window.hideSidebar() - 隐藏侧边栏');
            
            // 测试功能
            console.log('🧪 测试切换功能...');
            window.toggleSidebar();
            
        } catch (error) {
            console.error('❌ 修复失败:', error);
        }
        
    } else if (currentStatus.hasToggle && currentStatus.initialized) {
        console.log('✅ 扩展状态正常，可以直接使用');
        console.log('🧪 测试切换功能...');
        window.toggleSidebar();
        
    } else {
        console.log('❌ 扩展未正确加载，请检查：');
        console.log('   1. 扩展是否已安装并启用');
        console.log('   2. 页面是否已刷新');
        console.log('   3. 是否在特殊页面（chrome://等）');
    }
    
    // 最终状态检查
    setTimeout(() => {
        const finalStatus = {
            sidebar: !!document.getElementById('xpath-sidebar'),
            initialized: !!window.xpathSidebarInitialized,
            hasToggle: typeof window.toggleSidebar === 'function',
            hasInstance: !!window.xpathSidebarInstance
        };
        console.log('📊 修复后状态:', finalStatus);
        
        const allGood = Object.values(finalStatus).every(v => v === true);
        if (allGood) {
            console.log('🎉 所有功能正常！');
        } else {
            console.log('⚠️ 仍有问题，请查看故障排除指南');
        }
    }, 1000);
    
})();
