// 强制修复XPath显示问题
// 在浏览器控制台中运行此脚本

(function() {
    console.log('🔧 强制修复XPath显示问题...');
    
    // 确保侧边栏存在
    if (!document.getElementById('xpath-sidebar')) {
        console.log('侧边栏不存在，尝试创建...');
        if (typeof window.showSidebar === 'function') {
            window.showSidebar();
        }
    }
    
    // 等待侧边栏创建完成
    setTimeout(() => {
        
        // 强制重新定义关键函数
        console.log('\n🔧 重新定义关键函数...');
        
        // 重新定义getXPath函数
        window.getXPath = function(element) {
            console.log('🎯 getXPath调用，元素:', element.tagName);
            
            if (element.id) {
                const xpath = '//*[@id="' + element.id + '"]';
                console.log('🎯 使用ID生成XPath:', xpath);
                return xpath;
            }
            
            let path = '';
            let currentElement = element;
            while (currentElement && currentElement.nodeType === Node.ELEMENT_NODE) {
                let selector = currentElement.nodeName.toLowerCase();
                if (currentElement.className && currentElement.className.trim()) {
                    // 清理className，移除可能的特殊字符
                    const cleanClassName = currentElement.className.trim().replace(/\s+/g, ' ');
                    selector += '[@class="' + cleanClassName + '"]';
                }
                path = '/' + selector + path;
                currentElement = currentElement.parentNode;
                
                // 防止无限循环
                if (currentElement === document) break;
            }
            
            console.log('🎯 生成的XPath路径:', path);
            return path;
        };
        
        // 重新定义updateXPathInput函数
        window.updateXPathInput = function(xpath) {
            console.log('📝 updateXPathInput调用，XPath:', xpath);
            
            const input = document.getElementById('xpath-input');
            if (input) {
                input.value = xpath;
                console.log('📝 XPath已设置到输入框');
                
                // 立即执行XPath查询
                window.executeXPath();
            } else {
                console.error('❌ XPath输入框未找到');
            }
        };
        
        // 重新定义executeXPath函数
        window.executeXPath = function() {
            console.log('⚡ executeXPath调用');
            
            const input = document.getElementById('xpath-input');
            const xpath = input ? input.value.trim() : '';
            
            console.log('⚡ 要执行的XPath:', xpath);
            
            if (!xpath) {
                console.log('⚡ XPath为空，清空结果');
                window.updateResults([]);
                return;
            }
            
            try {
                console.log('⚡ 开始执行XPath查询...');
                const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                const elements = [];
                
                console.log('⚡ XPath查询结果数量:', result.snapshotLength);
                
                for (let i = 0; i < result.snapshotLength; i++) {
                    const element = result.snapshotItem(i);
                    const text = element.textContent ? element.textContent.trim().substring(0, 100) : '[无文本]';
                    elements.push({
                        text: text || '[无文本]',
                        tagName: element.tagName || 'TEXT',
                        element: element
                    });
                    console.log(`⚡ 结果 ${i + 1}:`, element.tagName, text.substring(0, 30));
                }
                
                window.updateResults(elements);
                console.log('✅ XPath执行成功，找到', elements.length, '个元素');
            } catch (error) {
                console.error('❌ XPath执行失败:', error);
                window.updateResults([]);
            }
        };
        
        // 重新定义updateResults函数
        window.updateResults = function(elements) {
            console.log('📊 updateResults调用，元素数量:', elements.length);
            
            const container = document.getElementById('xpath-results-container');
            const countElement = document.getElementById('xpath-results-count');
            
            if (!container) {
                console.error('❌ 结果容器未找到');
                return;
            }
            
            if (!countElement) {
                console.error('❌ 计数元素未找到');
                return;
            }
            
            // 更新计数
            countElement.textContent = elements.length + ' items';
            console.log('📊 计数已更新:', elements.length + ' items');
            
            // 清空容器
            container.innerHTML = '';
            console.log('📊 容器已清空');
            
            // 添加结果
            elements.forEach((item, index) => {
                const resultItem = document.createElement('div');
                resultItem.className = 'xpath-result-item';
                resultItem.innerHTML =
                    '<div class="xpath-result-tag">' + (item.tagName || 'TEXT') + '</div>' +
                    '<div class="xpath-result-text">' + (item.text || '[无文本]') + '</div>';
                
                // 添加点击事件
                if (item.element) {
                    resultItem.addEventListener('click', () => {
                        console.log('📊 结果项被点击:', item.tagName);
                        if (typeof window.highlightElement === 'function') {
                            window.highlightElement(item.element);
                        }
                        item.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    });
                }
                
                container.appendChild(resultItem);
                console.log(`📊 结果项 ${index + 1} 已添加:`, item.tagName, item.text.substring(0, 20));
            });
            
            console.log('✅ 结果显示更新完成');
        };
        
        // 重新设置Shift+点击事件
        console.log('\n⌨️ 重新设置Shift+点击事件...');
        
        // 移除现有的事件监听器
        document.removeEventListener('click', window.shiftClickHandler);
        
        // 添加新的事件监听器
        window.shiftClickHandler = function(e) {
            if (e.shiftKey) {
                console.log('🖱️ Shift+点击检测到:', {
                    target: e.target.tagName,
                    sidebarVisible: window.sidebarVisible,
                    hasSidebar: !!window.sidebar,
                    targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false
                });
                
                if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('🎯 开始处理Shift+点击...');
                    
                    // 生成XPath
                    const xpath = window.getXPath(e.target);
                    console.log('🎯 XPath生成完成:', xpath);
                    
                    // 更新输入框和结果
                    window.updateXPathInput(xpath);
                    console.log('🎯 XPath处理完成');
                } else {
                    console.log('🎯 条件不满足，跳过处理');
                }
            }
        };
        
        document.addEventListener('click', window.shiftClickHandler);
        
        // 测试功能
        console.log('\n🧪 测试修复效果...');
        
        // 测试XPath生成
        const testXPath = window.getXPath(document.body);
        console.log('🧪 测试XPath生成:', testXPath);
        
        // 测试输入框更新
        window.updateXPathInput(testXPath);
        
        // 检查结果
        setTimeout(() => {
            const input = document.getElementById('xpath-input');
            const container = document.getElementById('xpath-results-container');
            const count = document.getElementById('xpath-results-count');
            
            console.log('\n📊 修复后状态检查:');
            console.log('  输入框值:', input ? input.value : '未找到');
            console.log('  结果计数:', count ? count.textContent : '未找到');
            console.log('  结果项数量:', container ? container.children.length : '未找到');
            
            if (input && input.value && container && container.children.length > 0) {
                console.log('🎉 修复成功！XPath显示功能已恢复');
            } else {
                console.log('⚠️ 修复可能不完整，请检查控制台错误信息');
            }
            
            console.log('\n✅ 强制修复完成！');
            console.log('现在请按住Shift键并点击页面元素测试功能');
            
        }, 500);
        
    }, 200);
    
})();
