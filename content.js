// XPath Sidebar Content Script - 立即执行标记
console.log('🚀 XPath Sidebar content script loaded at:', new Date().toLocaleTimeString());

// 全局变量
window.isShiftPressed = false;
window.highlightedElement = null;
window.overlay = null;
window.sidebar = null;
window.sidebarVisible = false;
window.currentResults = [];

// 使用全局变量的别名，保持代码兼容性
let isShiftPressed = window.isShiftPressed;
let highlightedElement = window.highlightedElement;
let overlay = window.overlay;
let sidebar = window.sidebar;
let sidebarVisible = window.sidebarVisible;
let currentResults = window.currentResults;

// 初始化 - 全局函数
window.init = function() {
  console.log('XPath Sidebar init() called');
  try {
    window.createSidebar();
    window.setupEventListeners();
    console.log('XPath Sidebar initialized successfully');
  } catch (error) {
    console.error('Error initializing XPath Sidebar:', error);
  }
};

// 为了兼容性，也创建局部别名
const init = window.init;

// 创建侧边栏 - 全局函数
window.createSidebar = function() {
  if (window.sidebar) {
    console.log('Sidebar already exists');
    return;
  }

  console.log('Creating sidebar...');
  window.sidebar = document.createElement('div');
  window.sidebar.id = 'xpath-sidebar';
  sidebar = window.sidebar; // 更新局部引用
  sidebar.innerHTML = `
    <div class="xpath-sidebar-header">
      <span class="xpath-sidebar-title">XPath 测试器</span>
      <button class="xpath-close-btn" id="xpath-close-btn">×</button>
    </div>

    <div class="xpath-sidebar-tip">
      Hold shift and hover the element – Or edit XPath text
    </div>

    <div class="xpath-sidebar-input-section">
      <div class="xpath-sidebar-label">
        XPath
        <button class="xpath-copy-btn" id="xpath-copy-btn">Copy</button>
      </div>
      <textarea class="xpath-input" id="xpath-input" placeholder="输入XPath表达式..."></textarea>
    </div>

    <div class="xpath-sidebar-results-section">
      <div class="xpath-sidebar-results-header">
        <span>Results</span>
        <span class="xpath-results-count" id="xpath-results-count">0 items</span>
        <button class="xpath-copy-all-btn" id="xpath-copy-all-btn">Copy All</button>
      </div>
      <div class="xpath-results-container" id="xpath-results-container">
        <!-- 结果将在这里显示 -->
      </div>
    </div>
  `;

  document.body.appendChild(window.sidebar);
  window.setupSidebarEventListeners();
  console.log('Sidebar created and added to DOM');
};

// 设置侧边栏事件监听器 - 全局函数
window.setupSidebarEventListeners = function() {
  const closeBtn = sidebar.querySelector('#xpath-close-btn');
  const copyBtn = sidebar.querySelector('#xpath-copy-btn');
  const copyAllBtn = sidebar.querySelector('#xpath-copy-all-btn');
  const xpathInput = sidebar.querySelector('#xpath-input');

  closeBtn.addEventListener('click', window.hideSidebar);

  copyBtn.addEventListener('click', async () => {
    try {
      await navigator.clipboard.writeText(xpathInput.value);
      window.showFeedback(copyBtn, 'Copied!');
    } catch (err) {
      console.error('Failed to copy XPath:', err);
    }
  });

  copyAllBtn.addEventListener('click', async () => {
    try {
      const allText = currentResults.map(r => r.text).join('\n');
      await navigator.clipboard.writeText(allText);
      window.showFeedback(copyAllBtn, 'Copied!');
    } catch (err) {
      console.error('Failed to copy results:', err);
    }
  });

  xpathInput.addEventListener('input', window.debounce(window.executeXPath, 300));

  // 阻止侧边栏内的点击事件冒泡
  sidebar.addEventListener('click', (e) => {
    e.stopPropagation();
  });
};

// 设置全局事件监听器
function setupEventListeners() {
  // 监听键盘事件
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Shift') {
      isShiftPressed = true;
      if (sidebarVisible) {
        document.body.style.cursor = 'crosshair';
      }
    }
  });

  document.addEventListener('keyup', (e) => {
    if (e.key === 'Shift') {
      isShiftPressed = false;
      document.body.style.cursor = 'default';
      removeHighlight();
    }
  });

  // 鼠标悬停事件
  document.addEventListener('mouseover', (e) => {
    if (isShiftPressed && sidebarVisible && e.target !== overlay && !sidebar.contains(e.target)) {
      highlightElement(e.target);
    }
  });

  // 点击事件
  document.addEventListener('click', (e) => {
    if (isShiftPressed && sidebarVisible && !sidebar.contains(e.target)) {
      e.preventDefault();
      e.stopPropagation();
      const xpath = getXPath(e.target);
      updateXPathInput(xpath);
    }
  });
}

// 显示侧边栏 - 全局函数
window.showSidebar = function() {
  console.log('showSidebar called');
  if (!window.sidebar) {
    console.log('Sidebar not found, creating...');
    window.createSidebar();
  }

  window.sidebarVisible = true;
  sidebarVisible = window.sidebarVisible;
  window.sidebar.classList.add('xpath-sidebar-visible');
  document.body.classList.add('xpath-sidebar-open');
  console.log('Sidebar shown');
};

// 隐藏侧边栏 - 全局函数
window.hideSidebar = function() {
  window.sidebarVisible = false;
  sidebarVisible = window.sidebarVisible;
  window.sidebar.classList.remove('xpath-sidebar-visible');
  document.body.classList.remove('xpath-sidebar-open');
  window.removeHighlight();
  document.body.style.cursor = 'default';
};

// 切换侧边栏显示状态 - 全局函数
window.toggleSidebar = function() {
  console.log('toggleSidebar called, current state:', window.sidebarVisible);
  if (window.sidebarVisible) {
    window.hideSidebar();
  } else {
    window.showSidebar();
  }
};

function highlightElement(element) {
  removeHighlight();
  highlightedElement = element;

  const rect = element.getBoundingClientRect();
  overlay = document.createElement('div');
  overlay.className = 'xpath-highlight-overlay';
  overlay.style.cssText = `
    position: fixed !important;
    top: ${rect.top}px !important;
    left: ${rect.left}px !important;
    width: ${rect.width}px !important;
    height: ${rect.height}px !important;
  `;
  document.body.appendChild(overlay);
}

function removeHighlight() {
  if (overlay) {
    overlay.remove();
    overlay = null;
  }
}

function getXPath(element) {
  if (element.id) {
    return `//*[@id="${element.id}"]`;
  }

  const parts = [];
  while (element && element.nodeType === Node.ELEMENT_NODE) {
    let index = 0;
    let hasFollowingSiblings = false;
    let sibling = element.previousSibling;

    while (sibling) {
      if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === element.nodeName) {
        index++;
      }
      sibling = sibling.previousSibling;
    }

    sibling = element.nextSibling;
    while (sibling) {
      if (sibling.nodeType === Node.ELEMENT_NODE && sibling.nodeName === element.nodeName) {
        hasFollowingSiblings = true;
        break;
      }
      sibling = sibling.nextSibling;
    }

    const tagName = element.nodeName.toLowerCase();
    const pathIndex = (index > 0 || hasFollowingSiblings) ? `[${index + 1}]` : '';
    parts.splice(0, 0, tagName + pathIndex);

    element = element.parentNode;
  }

  return parts.length ? '/' + parts.join('/') : '';
}

// 更新XPath输入框
function updateXPathInput(xpath) {
  const xpathInput = sidebar.querySelector('#xpath-input');
  xpathInput.value = xpath;
  executeXPath();
}

// 执行XPath查询
function executeXPath() {
  const xpathInput = sidebar.querySelector('#xpath-input');
  const xpath = xpathInput.value.trim();

  if (!xpath) {
    updateResults([]);
    return;
  }

  try {
    const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
    const elements = [];

    for (let i = 0; i < result.snapshotLength; i++) {
      const element = result.snapshotItem(i);
      elements.push({
        text: element.textContent.trim().substring(0, 100),
        tagName: element.tagName,
        attributes: getElementAttributes(element)
      });
    }

    updateResults(elements);
  } catch (error) {
    updateResults([]);
  }
}

function getElementAttributes(element) {
  const attrs = {};
  for (let attr of element.attributes) {
    attrs[attr.name] = attr.value;
  }
  return attrs;
}

// 更新结果显示
function updateResults(results) {
  currentResults = results;
  const resultCount = sidebar.querySelector('#xpath-results-count');
  const resultsContainer = sidebar.querySelector('#xpath-results-container');

  resultCount.textContent = `${results.length} items`;
  resultsContainer.innerHTML = '';

  results.forEach((result) => {
    const item = document.createElement('div');
    item.className = 'xpath-result-item';
    item.textContent = result.text || '[空文本]';
    item.title = `标签: ${result.tagName}\n文本: ${result.text}`;

    item.addEventListener('click', async () => {
      try {
        await navigator.clipboard.writeText(result.text);
        showFeedback(item, 'Copied!');
      } catch (err) {
        console.error('Failed to copy result:', err);
      }
    });

    resultsContainer.appendChild(item);
  });
}

// 显示反馈信息
function showFeedback(element, message) {
  const originalText = element.textContent;
  element.textContent = message;
  element.style.background = 'var(--xpath-green)';
  element.style.color = 'var(--xpath-background-primary)';

  setTimeout(() => {
    element.textContent = originalText;
    element.style.background = '';
    element.style.color = '';
  }, 1000);
}

// 防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 监听来自background script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Received message:', request);
  if (request.action === 'toggleSidebar') {
    // 确保已经初始化
    if (!sidebar) {
      console.log('Sidebar not initialized, initializing now...');
      init();
    }
    toggleSidebar();
    sendResponse({ success: true });
  }
  return true; // 保持消息通道开放
});

// 初始化函数
function initializeXPathSidebar() {
  if (window.xpathSidebarInitialized) {
    console.log('XPath Sidebar already initialized');
    return;
  }

  console.log('Initializing XPath Sidebar...');

  try {
    init();

    // 设置初始化标志
    window.xpathSidebarInitialized = true;

    // 将关键函数暴露到全局作用域，便于调试
    window.toggleSidebar = toggleSidebar;
    window.showSidebar = showSidebar;
    window.hideSidebar = hideSidebar;

    // 创建实例对象
    window.xpathSidebarInstance = {
      sidebar,
      sidebarVisible,
      init,
      toggleSidebar,
      showSidebar,
      hideSidebar,
      createSidebar,
      executeXPath,
      updateResults
    };

    console.log('✅ XPath Sidebar initialization completed');
    console.log('📊 Instance created:', window.xpathSidebarInstance);

  } catch (error) {
    console.error('❌ XPath Sidebar initialization failed:', error);
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeXPathSidebar);
} else {
  initializeXPathSidebar();
}

// 确保在页面完全加载后也能初始化
window.addEventListener('load', () => {
  if (!window.xpathSidebarInitialized) {
    initializeXPathSidebar();
  }
});
