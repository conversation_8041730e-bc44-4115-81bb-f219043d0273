<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath选择器 - 独立版本</title>
    <style>
        /* 内嵌CSS样式 */
        :root {
            --primary-blue: #007AFF;
            --secondary-blue: #5AC8FA;
            --green: #34C759;
            --background-primary: #FFFFFF;
            --background-secondary: #F2F2F7;
            --text-primary: #000000;
            --text-secondary: #3C3C43;
            --separator: #3C3C4329;
            --shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            --border-radius: 12px;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --background-primary: #000000;
                --background-secondary: #1C1C1E;
                --text-primary: #FFFFFF;
                --text-secondary: #EBEBF5;
                --separator: #54545899;
            }
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--background-secondary);
            color: var(--text-primary);
            transition: margin-right 0.3s ease;
        }

        body.xpath-sidebar-open {
            margin-right: 50vw;
        }

        #xpath-sidebar {
            position: fixed;
            top: 0;
            right: -50vw;
            width: 50vw;
            height: 100vh;
            background: var(--background-primary);
            border-left: 1px solid var(--separator);
            box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
            z-index: 2147483647;
            display: flex;
            flex-direction: column;
            transition: right 0.3s ease;
        }

        #xpath-sidebar.visible {
            right: 0;
        }

        .sidebar-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--separator);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            color: var(--text-secondary);
        }

        .close-btn:hover {
            background: var(--background-secondary);
        }

        .sidebar-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .copy-btn {
            background: var(--green);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
        }

        .copy-btn:hover {
            opacity: 0.8;
        }

        textarea {
            width: 100%;
            height: 80px;
            background: var(--background-secondary);
            border: 1px solid var(--separator);
            border-radius: 8px;
            padding: 12px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 13px;
            color: var(--text-primary);
            resize: vertical;
            box-sizing: border-box;
        }

        textarea:focus {
            outline: none;
            border-color: var(--primary-blue);
        }

        .results-container {
            background: var(--background-secondary);
            border: 1px solid var(--separator);
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }

        .result-item {
            padding: 12px;
            border-bottom: 1px solid var(--separator);
            cursor: pointer;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
        }

        .result-item:hover {
            background: rgba(0, 122, 255, 0.1);
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .highlight-overlay {
            position: fixed;
            background: rgba(0, 122, 255, 0.15);
            border: 2px solid #007AFF;
            border-radius: 8px;
            pointer-events: none;
            z-index: 2147483646;
            box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
        }

        .toggle-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .toggle-btn:hover {
            opacity: 0.8;
        }

        .instructions {
            background: var(--background-primary);
            padding: 20px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .test-element {
            background: var(--background-primary);
            padding: 20px;
            margin: 20px 0;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-element:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .status-indicator {
            position: fixed;
            top: 80px;
            right: 20px;
            background: var(--green);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: none;
            z-index: 1000;
        }

        .status-indicator.shift-active {
            display: block;
            background: var(--secondary-blue);
        }
    </style>
</head>
<body>
    <button class="toggle-btn" onclick="toggleSidebar()">XPath选择器</button>
    <div class="status-indicator" id="statusIndicator">Shift模式激活</div>

    <div class="instructions">
        <h1>🎯 XPath选择器 - 独立版本</h1>
        <p><strong>使用说明：</strong></p>
        <ol>
            <li>点击右上角的"XPath选择器"按钮打开侧边栏</li>
            <li>按住 <kbd>Shift</kbd> 键</li>
            <li>将鼠标悬停在页面元素上（会显示蓝色高亮）</li>
            <li>点击元素自动生成XPath</li>
            <li>在侧边栏中查看和编辑XPath表达式</li>
        </ol>
        <p><strong>特点：</strong></p>
        <ul>
            <li>✅ 不依赖浏览器扩展</li>
            <li>✅ 支持file://协议</li>
            <li>✅ 实时XPath生成和验证</li>
            <li>✅ 苹果风格设计</li>
        </ul>
    </div>

    <div class="test-element" id="element-1">
        <h2>测试元素 1</h2>
        <p>这是一个带有ID的测试元素</p>
        <button onclick="alert('按钮被点击')">测试按钮</button>
    </div>

    <div class="test-element" class="special-class">
        <h2>测试元素 2</h2>
        <p>这是一个带有class的测试元素</p>
        <ul>
            <li>列表项 1</li>
            <li>列表项 2</li>
            <li>列表项 3</li>
        </ul>
    </div>

    <div class="test-element" data-test="example">
        <h2>测试元素 3</h2>
        <p>这是一个带有data属性的测试元素</p>
        <span>内联元素</span>
    </div>

    <div class="test-element">
        <h2>测试元素 4</h2>
        <p>这是一个普通的测试元素</p>
        <div>
            <p>嵌套段落</p>
            <a href="#" onclick="return false;">测试链接</a>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div id="xpath-sidebar">
        <div class="sidebar-header">
            <span class="sidebar-title">XPath 测试器</span>
            <button class="close-btn" onclick="hideSidebar()">×</button>
        </div>
        <div class="sidebar-content">
            <div class="section">
                <div class="section-title">
                    XPath表达式
                    <button class="copy-btn" onclick="copyXPath()">复制</button>
                </div>
                <textarea id="xpath-input" placeholder="XPath表达式将在这里显示..." oninput="executeXPath()"></textarea>
            </div>
            
            <div class="section">
                <div class="section-title">
                    匹配结果
                    <span id="result-count">0 个结果</span>
                </div>
                <div class="results-container" id="results-container">
                    <div class="result-item">暂无结果</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let sidebarVisible = false;
        let currentOverlay = null;
        let shiftPressed = false;

        // 切换侧边栏
        function toggleSidebar() {
            if (sidebarVisible) {
                hideSidebar();
            } else {
                showSidebar();
            }
        }

        // 显示侧边栏
        function showSidebar() {
            sidebarVisible = true;
            document.getElementById('xpath-sidebar').classList.add('visible');
            document.body.classList.add('xpath-sidebar-open');
            console.log('✅ 侧边栏已显示');
        }

        // 隐藏侧边栏
        function hideSidebar() {
            sidebarVisible = false;
            document.getElementById('xpath-sidebar').classList.remove('visible');
            document.body.classList.remove('xpath-sidebar-open');
            removeHighlight();
            console.log('✅ 侧边栏已隐藏');
        }

        // 复制XPath
        function copyXPath() {
            const input = document.getElementById('xpath-input');
            if (input.value) {
                navigator.clipboard.writeText(input.value).then(() => {
                    console.log('✅ XPath已复制');
                    // 临时显示反馈
                    const btn = event.target;
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    setTimeout(() => {
                        btn.textContent = originalText;
                    }, 1000);
                });
            }
        }

        // 生成XPath
        function generateXPath(element) {
            // 优先使用ID
            if (element.id) {
                return `//*[@id="${element.id}"]`;
            }

            // 使用class
            if (element.className && typeof element.className === 'string') {
                const classes = element.className.trim().split(/\s+/);
                if (classes.length === 1) {
                    return `//${element.tagName.toLowerCase()}[@class="${classes[0]}"]`;
                }
            }

            // 使用data属性
            for (let attr of element.attributes) {
                if (attr.name.startsWith('data-')) {
                    return `//${element.tagName.toLowerCase()}[@${attr.name}="${attr.value}"]`;
                }
            }

            // 生成路径
            let path = '';
            let current = element;
            
            while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
                let selector = current.tagName.toLowerCase();
                
                // 添加位置信息
                if (current.parentNode) {
                    const siblings = Array.from(current.parentNode.children).filter(
                        child => child.tagName === current.tagName
                    );
                    if (siblings.length > 1) {
                        const index = siblings.indexOf(current) + 1;
                        selector += `[${index}]`;
                    }
                }
                
                path = '/' + selector + path;
                current = current.parentNode;
            }
            
            return path || `//${element.tagName.toLowerCase()}`;
        }

        // 执行XPath
        function executeXPath() {
            const input = document.getElementById('xpath-input');
            const xpath = input.value.trim();
            
            if (!xpath) {
                updateResults([]);
                return;
            }

            try {
                const result = document.evaluate(
                    xpath, 
                    document, 
                    null, 
                    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, 
                    null
                );
                
                const elements = [];
                for (let i = 0; i < result.snapshotLength; i++) {
                    const element = result.snapshotItem(i);
                    elements.push({
                        text: element.textContent.trim().substring(0, 100) || '[无文本]',
                        tagName: element.tagName,
                        element: element
                    });
                }
                
                updateResults(elements);
                console.log(`✅ XPath执行成功，找到 ${elements.length} 个元素`);
                
            } catch (error) {
                console.error('❌ XPath执行失败:', error);
                updateResults([]);
            }
        }

        // 更新结果显示
        function updateResults(results) {
            const container = document.getElementById('results-container');
            const countEl = document.getElementById('result-count');
            
            countEl.textContent = `${results.length} 个结果`;
            
            if (results.length === 0) {
                container.innerHTML = '<div class="result-item">暂无结果</div>';
                return;
            }
            
            container.innerHTML = '';
            results.forEach((result, index) => {
                const item = document.createElement('div');
                item.className = 'result-item';
                item.textContent = `${index + 1}. ${result.text}`;
                item.title = `标签: ${result.tagName}\n文本: ${result.text}`;
                
                item.onclick = () => {
                    navigator.clipboard.writeText(result.text).then(() => {
                        console.log('✅ 结果文本已复制');
                    });
                };
                
                container.appendChild(item);
            });
        }

        // 高亮元素
        function highlightElement(element) {
            removeHighlight();
            
            const rect = element.getBoundingClientRect();
            const overlay = document.createElement('div');
            overlay.className = 'highlight-overlay';
            overlay.style.top = rect.top + 'px';
            overlay.style.left = rect.left + 'px';
            overlay.style.width = rect.width + 'px';
            overlay.style.height = rect.height + 'px';
            
            document.body.appendChild(overlay);
            currentOverlay = overlay;
        }

        // 移除高亮
        function removeHighlight() {
            if (currentOverlay) {
                currentOverlay.remove();
                currentOverlay = null;
            }
        }

        // 事件监听器
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && !shiftPressed) {
                shiftPressed = true;
                if (sidebarVisible) {
                    document.body.style.cursor = 'crosshair';
                    document.getElementById('statusIndicator').classList.add('shift-active');
                    console.log('🎯 Shift模式已激活');
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                shiftPressed = false;
                document.body.style.cursor = 'default';
                document.getElementById('statusIndicator').classList.remove('shift-active');
                removeHighlight();
                console.log('🎯 Shift模式已关闭');
            }
        });

        document.addEventListener('mouseover', (e) => {
            if (shiftPressed && sidebarVisible) {
                const sidebar = document.getElementById('xpath-sidebar');
                if (!sidebar.contains(e.target)) {
                    highlightElement(e.target);
                }
            }
        });

        document.addEventListener('click', (e) => {
            if (shiftPressed && sidebarVisible) {
                const sidebar = document.getElementById('xpath-sidebar');
                if (!sidebar.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    const xpath = generateXPath(e.target);
                    document.getElementById('xpath-input').value = xpath;
                    executeXPath();
                    
                    console.log('🎯 元素已选择，XPath:', xpath);
                    removeHighlight();
                }
            }
        });

        // 页面加载完成
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 XPath选择器已准备就绪');
        });
    </script>
</body>
</html>
