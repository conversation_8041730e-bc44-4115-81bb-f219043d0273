<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath测试器 - 最终测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-element {
            padding: 15px;
            margin: 10px 0;
            background: #f0f0f0;
            border-radius: 8px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .test-element:hover {
            border-color: #007AFF;
            background: #f8f9ff;
        }
        .instructions {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        .success {
            background: #34C759;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            background: #FF3B30;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .warning {
            background: #FF9500;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 XPath测试器 - 最终测试</h1>
        <p>这是验证扩展功能的最终测试页面</p>
    </div>

    <div class="instructions">
        <h2>📋 测试步骤</h2>
        <ol>
            <li><strong>重新加载扩展</strong> - 在 chrome://extensions/ 中刷新扩展</li>
            <li><strong>点击扩展图标</strong> - 侧边栏应从右侧滑出</li>
            <li><strong>测试Shift+点击</strong> - 按住Shift键点击下面的元素</li>
            <li><strong>验证XPath生成</strong> - 检查侧边栏中是否显示XPath</li>
        </ol>
    </div>

    <div class="status-card">
        <h2>🔍 实时状态监控</h2>
        <div id="status-display">正在检查扩展状态...</div>
    </div>

    <div class="status-card">
        <h2>🧪 测试元素</h2>
        <p>按住 <kbd>Shift</kbd> 键并点击下面的元素来测试XPath生成：</p>
        
        <div class="test-element" id="element-1" data-test="first">
            <h3>测试元素 1</h3>
            <p>这是第一个测试元素，ID为 "element-1"</p>
        </div>
        
        <div class="test-element" class="special-class" data-test="second">
            <h3>测试元素 2</h3>
            <p>这是第二个测试元素，带有特殊class</p>
            <button onclick="alert('按钮被点击')">测试按钮</button>
        </div>
        
        <div class="test-element" data-test="third">
            <h3>测试元素 3</h3>
            <ul>
                <li>列表项 1</li>
                <li>列表项 2</li>
                <li>列表项 3</li>
            </ul>
        </div>
    </div>

    <script>
        let statusDisplay = document.getElementById('status-display');
        
        function updateStatus(message, type = 'info') {
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : '';
            
            statusDisplay.innerHTML = `<div class="${className}">${message}</div>`;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function checkExtensionStatus() {
            const checks = {
                sidebar: !!document.getElementById('xpath-sidebar'),
                initialized: !!window.xpathSidebarInitialized,
                hasToggle: typeof window.toggleSidebar === 'function',
                hasInstance: !!window.xpathSidebarInstance
            };
            
            const passedChecks = Object.values(checks).filter(v => v).length;
            const totalChecks = Object.keys(checks).length;
            
            let message = `状态检查: ${passedChecks}/${totalChecks} 通过<br>`;
            message += `• 侧边栏元素: ${checks.sidebar ? '✅' : '❌'}<br>`;
            message += `• 初始化状态: ${checks.initialized ? '✅' : '❌'}<br>`;
            message += `• 切换函数: ${checks.hasToggle ? '✅' : '❌'}<br>`;
            message += `• 实例对象: ${checks.hasInstance ? '✅' : '❌'}`;
            
            if (passedChecks === totalChecks) {
                updateStatus(message + '<br><br>🎉 <strong>扩展完全正常！可以开始测试功能</strong>', 'success');
            } else if (passedChecks > 0) {
                updateStatus(message + '<br><br>⚠️ <strong>扩展部分加载，尝试点击扩展图标</strong>', 'warning');
            } else {
                updateStatus(message + '<br><br>❌ <strong>扩展未加载，请检查安装</strong>', 'error');
            }
            
            return checks;
        }
        
        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            updateStatus('🎉 侧边栏已创建！扩展正在工作', 'success');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 监听键盘事件
        let shiftPressed = false;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && !shiftPressed) {
                shiftPressed = true;
                updateStatus('⌨️ Shift键已按下，现在可以点击元素生成XPath', 'info');
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                shiftPressed = false;
                updateStatus('⌨️ Shift键已释放', 'info');
            }
        });
        
        // 监听点击事件
        document.addEventListener('click', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                const element = e.target.closest('.test-element');
                updateStatus(`🖱️ Shift+点击了: ${element.querySelector('h3').textContent}`, 'info');
            }
        });
        
        // 定期检查状态
        setInterval(checkExtensionStatus, 2000);
        
        // 页面加载完成后立即检查
        window.addEventListener('load', () => {
            setTimeout(checkExtensionStatus, 500);
        });
    </script>
</body>
</html>
