// XPath扩展修复验证脚本
// 在浏览器控制台中运行此脚本来快速验证所有修复

(function() {
    console.log('🔍 XPath扩展修复验证开始...');
    
    const results = {
        closeButton: false,
        clearButton: false,
        sidebarWidth: false,
        buttonGroup: false,
        eventListeners: false,
        functions: false
    };
    
    // 检查1: 关闭按钮
    console.log('\n1️⃣ 检查关闭按钮修复...');
    if (typeof window.showSidebar === 'function') {
        window.showSidebar();
        setTimeout(() => {
            const closeBtn = document.getElementById('xpath-close-btn');
            if (closeBtn) {
                console.log('  ✅ 关闭按钮存在');
                // 测试点击事件
                const hasClickListener = closeBtn.onclick || closeBtn.addEventListener;
                console.log(`  ${hasClickListener ? '✅' : '❌'} 关闭按钮有事件监听器`);
                results.closeButton = true;
            } else {
                console.log('  ❌ 关闭按钮不存在');
            }
            
            // 检查2: Clear按钮
            console.log('\n2️⃣ 检查Clear按钮...');
            const clearBtn = document.getElementById('xpath-clear-btn');
            if (clearBtn) {
                console.log('  ✅ Clear按钮存在');
                console.log(`  ✅ Clear按钮颜色: ${window.getComputedStyle(clearBtn).backgroundColor}`);
                results.clearButton = true;
            } else {
                console.log('  ❌ Clear按钮不存在');
            }
            
            // 检查3: 按钮组
            console.log('\n3️⃣ 检查按钮组布局...');
            const buttonGroup = document.querySelector('.xpath-button-group');
            if (buttonGroup) {
                console.log('  ✅ 按钮组存在');
                const buttons = buttonGroup.querySelectorAll('button');
                console.log(`  ✅ 按钮组包含 ${buttons.length} 个按钮`);
                results.buttonGroup = true;
            } else {
                console.log('  ❌ 按钮组不存在');
            }
            
            // 检查4: 侧边栏宽度
            console.log('\n4️⃣ 检查侧边栏宽度...');
            const sidebar = document.getElementById('xpath-sidebar');
            if (sidebar) {
                const styles = window.getComputedStyle(sidebar);
                const width = styles.width;
                const widthPercent = (parseFloat(width) / window.innerWidth) * 100;
                console.log(`  📏 侧边栏宽度: ${width} (${widthPercent.toFixed(1)}%)`);
                if (widthPercent <= 40) {
                    console.log('  ✅ 宽度已调整为合适大小');
                    results.sidebarWidth = true;
                } else {
                    console.log('  ❌ 宽度仍然过大');
                }
            }
            
            // 检查5: 函数存在性
            console.log('\n5️⃣ 检查新增函数...');
            const newFunctions = ['clearXPath', 'setupSidebarEventListeners'];
            let allFunctionsExist = true;
            newFunctions.forEach(func => {
                const exists = typeof window[func] === 'function';
                console.log(`  ${exists ? '✅' : '❌'} ${func}`);
                if (!exists) allFunctionsExist = false;
            });
            results.functions = allFunctionsExist;
            
            // 检查6: 事件监听器
            console.log('\n6️⃣ 检查事件监听器设置...');
            if (typeof window.setupSidebarEventListeners === 'function') {
                console.log('  ✅ setupSidebarEventListeners 函数存在');
                results.eventListeners = true;
            } else {
                console.log('  ❌ setupSidebarEventListeners 函数不存在');
            }
            
            // 总结
            console.log('\n📊 修复验证总结:');
            console.log('='.repeat(40));
            Object.entries(results).forEach(([key, value]) => {
                const status = value ? '✅ 通过' : '❌ 失败';
                const description = {
                    closeButton: '关闭按钮修复',
                    clearButton: 'Clear按钮添加',
                    sidebarWidth: '侧边栏宽度调整',
                    buttonGroup: '按钮组布局',
                    eventListeners: '事件监听器设置',
                    functions: '新增函数'
                };
                console.log(`${status} ${description[key]}`);
            });
            
            const passedCount = Object.values(results).filter(Boolean).length;
            const totalCount = Object.keys(results).length;
            
            console.log('\n🎯 总体结果:');
            if (passedCount === totalCount) {
                console.log('🎉 所有修复都已成功应用！');
                console.log('现在可以测试以下功能:');
                console.log('  • 点击关闭按钮（×）');
                console.log('  • 点击Clear按钮（橙色）清除XPath');
                console.log('  • 点击Copy按钮（绿色）复制XPath');
                console.log('  • Shift+点击元素生成XPath');
            } else {
                console.log(`⚠️  ${passedCount}/${totalCount} 项修复成功，还有 ${totalCount - passedCount} 项需要检查`);
            }
            
        }, 500);
    } else {
        console.log('❌ showSidebar函数不存在，扩展可能未正确加载');
    }
})();
