<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath测试器 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1d1d1f;
            font-size: 32px;
            font-weight: 600;
            margin-bottom: 20px;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #d2d2d7;
            border-radius: 8px;
        }
        .button {
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
        }
        .list-item {
            padding: 10px;
            margin: 5px 0;
            background: #f2f2f7;
            border-radius: 6px;
        }
        #special-element {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>XPath测试器演示页面</h1>

        <div class="section">
            <h2>使用说明</h2>
            <p>1. 点击浏览器工具栏中的XPath测试器图标</p>
            <p>2. 侧边栏将从右侧滑出，占据浏览器窗口的一半</p>
            <p>3. 按住Shift键并将鼠标悬停在页面元素上</p>
            <p>4. 点击元素自动生成XPath并填入侧边栏</p>
            <p>5. 在侧边栏中编辑和测试XPath表达式</p>
            <p>6. 点击页面其他地方不会关闭侧边栏</p>
        </div>

        <div class="section">
            <h2>测试元素</h2>
            <button class="button" id="test-button">测试按钮</button>
            <button class="button">另一个按钮</button>

            <ul>
                <li class="list-item">列表项目 1</li>
                <li class="list-item">列表项目 2</li>
                <li class="list-item">列表项目 3</li>
            </ul>
        </div>

        <div class="section">
            <h2>特殊元素</h2>
            <div id="special-element">
                这是一个带有特殊ID的元素
            </div>
        </div>

        <div class="section">
            <h2>表格数据</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f2f2f7;">
                        <th style="padding: 12px; border: 1px solid #d2d2d7;">姓名</th>
                        <th style="padding: 12px; border: 1px solid #d2d2d7;">年龄</th>
                        <th style="padding: 12px; border: 1px solid #d2d2d7;">城市</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">张三</td>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">25</td>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">北京</td>
                    </tr>
                    <tr>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">李四</td>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">30</td>
                        <td style="padding: 12px; border: 1px solid #d2d2d7;">上海</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>常用XPath示例</h2>
            <p><code>//button[@id='test-button']</code> - 选择特定ID的按钮</p>
            <p><code>//li[@class='list-item']</code> - 选择所有列表项</p>
            <p><code>//table//td[contains(text(),'张三')]</code> - 选择包含特定文本的单元格</p>
            <p><code>//*[@id='special-element']</code> - 选择特殊元素</p>
        </div>
    </div>

    <script>
        document.getElementById('test-button').addEventListener('click', function() {
            alert('测试按钮被点击了！');
        });
    </script>
</body>
</html>
