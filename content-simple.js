// XPath Sidebar Content Script - 简化版本
console.log('🚀 XPath Sidebar (Simple) loaded at:', new Date().toLocaleTimeString());

// 立即设置全局变量和函数
(function() {
    // 全局状态变量
    window.xpathSidebarInitialized = false;
    window.sidebarVisible = false;
    window.sidebar = null;
    window.currentResults = [];

    // 立即暴露主要函数到全局作用域
    window.toggleSidebar = function() {
        console.log('🔄 toggleSidebar called, current state:', window.sidebarVisible);
        if (window.sidebarVisible) {
            window.hideSidebar();
        } else {
            window.showSidebar();
        }
    };

    window.showSidebar = function() {
        console.log('📖 showSidebar called');
        if (!window.sidebar) {
            console.log('Creating sidebar...');
            window.createSidebar();
        }

        window.sidebarVisible = true;
        window.sidebar.classList.add('xpath-sidebar-visible');
        document.body.classList.add('xpath-sidebar-open');
        console.log('✅ Sidebar shown');
    };

    window.hideSidebar = function() {
        console.log('📕 hideSidebar called');
        window.sidebarVisible = false;
        if (window.sidebar) {
            window.sidebar.classList.remove('xpath-sidebar-visible');
        }
        document.body.classList.remove('xpath-sidebar-open');
        document.body.style.cursor = 'default';
        console.log('✅ Sidebar hidden');
    };

    window.createSidebar = function() {
        if (window.sidebar) {
            console.log('Sidebar already exists');
            return;
        }

        console.log('🏗️ Creating sidebar...');
        window.sidebar = document.createElement('div');
        window.sidebar.id = 'xpath-sidebar';
        window.sidebar.innerHTML = `
            <div class="xpath-sidebar-header">
                <span class="xpath-sidebar-title">XPath 测试器</span>
                <button class="xpath-close-btn" onclick="window.hideSidebar()">×</button>
            </div>

            <div class="xpath-sidebar-tip">
                Hold shift and hover the element – Or edit XPath text
            </div>

            <div class="xpath-sidebar-input-section">
                <div class="xpath-sidebar-label">
                    XPath
                    <button class="xpath-copy-btn" onclick="window.copyXPath()">Copy</button>
                </div>
                <textarea class="xpath-input" id="xpath-input" placeholder="输入XPath表达式..."></textarea>
            </div>

            <div class="xpath-sidebar-results-section">
                <div class="xpath-sidebar-results-header">
                    <span>Results</span>
                    <span class="xpath-results-count" id="xpath-results-count">0 items</span>
                    <button class="xpath-copy-all-btn" onclick="window.copyAllResults()">Copy All</button>
                </div>
                <div class="xpath-results-container" id="xpath-results-container">
                    <!-- 结果将在这里显示 -->
                </div>
            </div>
        `;

        document.body.appendChild(window.sidebar);
        window.setupEventListeners();
        console.log('✅ Sidebar created and added to DOM');
    };

    window.copyXPath = function() {
        const input = document.getElementById('xpath-input');
        if (input && input.value) {
            navigator.clipboard.writeText(input.value).then(() => {
                console.log('✅ XPath copied');
            }).catch(err => {
                console.error('❌ Copy failed:', err);
            });
        }
    };

    window.copyAllResults = function() {
        if (window.currentResults && window.currentResults.length > 0) {
            const allText = window.currentResults.map(r => r.text).join('\n');
            navigator.clipboard.writeText(allText).then(() => {
                console.log('✅ All results copied');
            }).catch(err => {
                console.error('❌ Copy failed:', err);
            });
        }
    };

    window.setupEventListeners = function() {
        console.log('🎧 Setting up event listeners...');

        // Shift键监听
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && window.sidebarVisible) {
                document.body.style.cursor = 'crosshair';
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                document.body.style.cursor = 'default';
                window.removeHighlight();
            }
        });

        // 鼠标事件监听
        document.addEventListener('mouseover', (e) => {
            if (e.shiftKey && window.sidebarVisible && !window.sidebar.contains(e.target)) {
                window.highlightElement(e.target);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.shiftKey && window.sidebarVisible && !window.sidebar.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                const xpath = window.getXPath(e.target);
                window.updateXPathInput(xpath);
            }
        });
    };

    window.highlightElement = function(element) {
        window.removeHighlight();

        const rect = element.getBoundingClientRect();
        const overlay = document.createElement('div');
        overlay.className = 'xpath-highlight-overlay';
        overlay.style.cssText = `
            position: fixed !important;
            top: ${rect.top}px !important;
            left: ${rect.left}px !important;
            width: ${rect.width}px !important;
            height: ${rect.height}px !important;
        `;
        document.body.appendChild(overlay);
        window.currentOverlay = overlay;
    };

    window.removeHighlight = function() {
        if (window.currentOverlay) {
            window.currentOverlay.remove();
            window.currentOverlay = null;
        }
    };

    window.getXPath = function(element) {
        if (element.id) {
            return `//*[@id="${element.id}"]`;
        }

        let path = '';
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let selector = element.nodeName.toLowerCase();
            if (element.className) {
                selector += `[@class="${element.className}"]`;
            }
            path = '/' + selector + path;
            element = element.parentNode;
        }
        return path;
    };

    window.updateXPathInput = function(xpath) {
        const input = document.getElementById('xpath-input');
        if (input) {
            input.value = xpath;
            window.executeXPath();
        }
    };

    window.executeXPath = function() {
        const input = document.getElementById('xpath-input');
        const xpath = input ? input.value.trim() : '';

        if (!xpath) {
            window.updateResults([]);
            return;
        }

        try {
            const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
            const elements = [];

            for (let i = 0; i < result.snapshotLength; i++) {
                const element = result.snapshotItem(i);
                elements.push({
                    text: element.textContent.trim().substring(0, 100),
                    tagName: element.tagName
                });
            }

            window.updateResults(elements);
        } catch (error) {
            console.error('XPath execution error:', error);
            window.updateResults([]);
        }
    };

    window.updateResults = function(results) {
        window.currentResults = results;
        const countEl = document.getElementById('xpath-results-count');
        const containerEl = document.getElementById('xpath-results-container');

        if (countEl) {
            countEl.textContent = `${results.length} items`;
        }

        if (containerEl) {
            containerEl.innerHTML = '';
            results.forEach((result, index) => {
                const item = document.createElement('div');
                item.className = 'xpath-result-item';
                item.textContent = result.text || '[空文本]';
                item.title = `标签: ${result.tagName}\n文本: ${result.text}`;
                item.onclick = () => {
                    navigator.clipboard.writeText(result.text);
                };
                containerEl.appendChild(item);
            });
        }
    };

    // 初始化函数
    window.initializeXPathSidebar = function() {
        if (window.xpathSidebarInitialized) {
            console.log('Already initialized');
            return;
        }

        console.log('🔄 Initializing XPath Sidebar...');

        try {
            window.setupEventListeners();

            // 创建实例对象
            window.xpathSidebarInstance = {
                sidebar: window.sidebar,
                sidebarVisible: window.sidebarVisible,
                toggleSidebar: window.toggleSidebar,
                showSidebar: window.showSidebar,
                hideSidebar: window.hideSidebar,
                createSidebar: window.createSidebar
            };

            window.xpathSidebarInitialized = true;
            console.log('✅ XPath Sidebar initialization completed');

        } catch (error) {
            console.error('❌ Initialization failed:', error);
        }
    };

    // 消息监听器
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('📨 Received message:', request);
        if (request.action === 'toggleSidebar') {
            if (!window.xpathSidebarInitialized) {
                window.initializeXPathSidebar();
            }
            window.toggleSidebar();
            sendResponse({ success: true });
        }
        return true;
    });

    // 立即初始化
    console.log('🚀 Starting immediate initialization...');
    window.initializeXPathSidebar();

    console.log('📊 Global functions available:', {
        toggleSidebar: typeof window.toggleSidebar,
        showSidebar: typeof window.showSidebar,
        hideSidebar: typeof window.hideSidebar,
        createSidebar: typeof window.createSidebar,
        initialized: window.xpathSidebarInitialized
    });

})();

console.log('✅ XPath Sidebar (Simple) setup completed');
