// 快速测试XPath显示功能
// 在浏览器控制台中运行此脚本

(function() {
    console.log('🚀 快速测试XPath显示功能...');
    
    // 确保侧边栏存在
    if (!document.getElementById('xpath-sidebar')) {
        console.log('显示侧边栏...');
        if (typeof window.showSidebar === 'function') {
            window.showSidebar();
        }
    }
    
    setTimeout(() => {
        console.log('\n🧪 开始测试...');
        
        // 测试1: 检查DOM元素
        const input = document.getElementById('xpath-input');
        const container = document.getElementById('xpath-results-container');
        const count = document.getElementById('xpath-results-count');
        
        console.log('DOM元素检查:');
        console.log('  输入框:', !!input);
        console.log('  结果容器:', !!container);
        console.log('  计数元素:', !!count);
        
        if (!input || !container || !count) {
            console.error('❌ 关键DOM元素缺失，无法继续测试');
            return;
        }
        
        // 测试2: 手动生成XPath并显示
        console.log('\n🎯 测试XPath生成和显示...');
        
        const testElement = document.body;
        console.log('测试元素:', testElement.tagName);
        
        // 生成XPath
        if (typeof window.getXPath === 'function') {
            const xpath = window.getXPath(testElement);
            console.log('生成的XPath:', xpath);
            
            // 设置到输入框
            input.value = xpath;
            console.log('输入框设置完成');
            
            // 执行XPath查询
            if (typeof window.executeXPath === 'function') {
                console.log('执行XPath查询...');
                window.executeXPath();
                
                // 检查结果
                setTimeout(() => {
                    console.log('\n📊 结果检查:');
                    console.log('  输入框值:', input.value);
                    console.log('  计数显示:', count.textContent);
                    console.log('  结果项数量:', container.children.length);
                    
                    if (container.children.length > 0) {
                        console.log('✅ XPath显示功能正常！');
                        console.log('现在可以尝试Shift+点击功能');
                    } else {
                        console.log('❌ 结果未显示，可能存在问题');
                        
                        // 尝试手动添加一个测试结果
                        console.log('尝试手动添加测试结果...');
                        const testResult = document.createElement('div');
                        testResult.className = 'xpath-result-item';
                        testResult.innerHTML = 
                            '<div class="xpath-result-tag">TEST</div>' +
                            '<div class="xpath-result-text">测试结果项</div>';
                        container.appendChild(testResult);
                        count.textContent = '1 items';
                        
                        console.log('手动测试结果已添加');
                    }
                }, 200);
            }
        }
        
        // 测试3: 模拟Shift+点击
        console.log('\n⌨️ 准备Shift+点击测试...');
        console.log('请按住Shift键并点击页面上的任意元素');
        
        // 添加临时监听器来监控Shift+点击
        let tempHandler = function(e) {
            if (e.shiftKey) {
                console.log('🎯 检测到Shift+点击:', e.target.tagName);
                
                // 移除监听器避免重复
                document.removeEventListener('click', tempHandler);
                
                // 检查是否会触发XPath更新
                const initialValue = input.value;
                setTimeout(() => {
                    if (input.value !== initialValue) {
                        console.log('✅ Shift+点击功能正常，XPath已更新');
                        console.log('  新XPath:', input.value);
                    } else {
                        console.log('❌ Shift+点击可能有问题，XPath未更新');
                    }
                }, 100);
            }
        };
        
        document.addEventListener('click', tempHandler);
        
        // 10秒后移除监听器
        setTimeout(() => {
            document.removeEventListener('click', tempHandler);
        }, 10000);
        
    }, 300);
    
})();
