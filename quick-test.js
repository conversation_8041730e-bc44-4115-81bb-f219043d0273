// 快速测试脚本 - 在浏览器控制台中运行
console.log('🧪 开始快速测试...');

// 手动加载测试版本的content script
const script = document.createElement('script');
script.src = 'content-test.js';
script.onload = () => {
    console.log('✅ 测试脚本加载完成');
    
    // 等待一下再检查
    setTimeout(() => {
        console.log('🔍 检查全局函数...');
        
        const checks = {
            initialized: !!window.xpathSidebarInitialized,
            toggleSidebar: typeof window.toggleSidebar === 'function',
            showSidebar: typeof window.showSidebar === 'function',
            hideSidebar: typeof window.hideSidebar === 'function',
            createSidebar: typeof window.createSidebar === 'function',
            instance: !!window.xpathSidebarInstance
        };
        
        console.log('📊 检查结果:', checks);
        
        const passed = Object.values(checks).filter(v => v).length;
        const total = Object.keys(checks).length;
        
        if (passed === total) {
            console.log('🎉 所有检查通过！测试切换功能...');
            window.toggleSidebar();
        } else {
            console.log(`❌ 检查失败: ${passed}/${total} 通过`);
        }
        
    }, 500);
};

script.onerror = () => {
    console.error('❌ 脚本加载失败');
};

document.head.appendChild(script);
