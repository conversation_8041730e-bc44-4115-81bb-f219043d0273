// 调试当前XPath扩展状态
// 在浏览器控制台中运行此脚本

(function() {
    console.log('🔍 调试XPath扩展当前状态...');
    
    // 检查基本状态
    console.log('\n📊 基本状态:');
    console.log('  sidebarVisible:', window.sidebarVisible);
    console.log('  sidebar exists:', !!window.sidebar);
    console.log('  initialized:', window.xpathSidebarInitialized);
    
    // 检查函数
    console.log('\n🔧 函数检查:');
    const functions = [
        'toggleSidebar', 'showSidebar', 'hideSidebar', 'createSidebar',
        'setupEventListeners', 'setupSidebarEventListeners',
        'highlightElement', 'removeHighlight', 'getXPath', 'updateXPathInput',
        'executeXPath', 'updateResults', 'copyXPath', 'clearXPath'
    ];
    
    functions.forEach(func => {
        console.log(`  ${func}: ${typeof window[func] === 'function' ? '✅' : '❌'}`);
    });
    
    // 如果侧边栏存在，检查DOM元素
    if (window.sidebar) {
        console.log('\n🎯 DOM元素检查:');
        const elements = [
            'xpath-sidebar',
            'xpath-close-btn', 
            'xpath-copy-btn',
            'xpath-clear-btn',
            'xpath-input',
            'xpath-results-container'
        ];
        
        elements.forEach(id => {
            const element = document.getElementById(id);
            console.log(`  #${id}: ${element ? '✅' : '❌'}`);
            if (element && id.includes('btn')) {
                // 检查按钮是否有事件监听器
                const hasListeners = element.onclick || element.addEventListener;
                console.log(`    事件监听器: ${hasListeners ? '✅' : '❌'}`);
            }
        });
    } else {
        console.log('\n❌ 侧边栏不存在，尝试创建...');
        if (typeof window.showSidebar === 'function') {
            window.showSidebar();
            setTimeout(() => {
                console.log('侧边栏创建后，重新运行此脚本检查状态');
            }, 500);
        }
    }
    
    // 测试关闭按钮
    console.log('\n🔴 测试关闭按钮:');
    const closeBtn = document.getElementById('xpath-close-btn');
    if (closeBtn) {
        console.log('  关闭按钮存在: ✅');
        console.log('  按钮HTML:', closeBtn.outerHTML);
        
        // 手动添加事件监听器作为备用
        closeBtn.addEventListener('click', function(e) {
            console.log('🔴 手动添加的关闭按钮事件触发');
            e.preventDefault();
            e.stopPropagation();
            if (typeof window.hideSidebar === 'function') {
                window.hideSidebar();
            }
        });
        console.log('  手动事件监听器已添加');
    } else {
        console.log('  关闭按钮不存在: ❌');
    }
    
    // 测试Shift功能
    console.log('\n⌨️ 测试Shift功能:');
    console.log('  请按住Shift键并移动鼠标到页面元素上');
    console.log('  如果看到十字光标和蓝色高亮框，说明Shift功能正常');
    
    // 添加临时的Shift测试
    let shiftTestAdded = false;
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Shift' && !shiftTestAdded) {
            console.log('🎯 Shift键按下检测到！');
            shiftTestAdded = true;
        }
    });
    
    console.log('\n✅ 调试完成！');
    console.log('如果关闭按钮仍然不工作，请手动点击它测试手动添加的事件监听器');
})();
