# XPath 测试器 - 安装指南

## 🚀 快速安装

### 方法一：开发者模式安装（推荐）

1. **打开 Chrome 扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择本项目的文件夹
   - 点击"选择文件夹"

4. **完成安装**
   - 扩展会出现在扩展列表中
   - 浏览器工具栏会显示扩展图标

## ✅ 验证安装

1. **检查扩展图标**
   - 工具栏应该显示 XPath 测试器图标

2. **测试功能**
   - 打开 `test.html` 文件
   - 点击扩展图标，侧边栏应从右侧滑出
   - 按住 Shift 键悬停页面元素，应显示蓝色高亮
   - 点击元素，XPath应自动填入侧边栏
   - 点击页面其他地方，侧边栏应保持打开状态

## 🔧 故障排除

### 扩展无法加载
- 确保所有文件都在同一文件夹中
- 检查 `manifest.json` 文件是否存在
- 重新启用开发者模式

### 功能不正常
- 刷新扩展：在扩展管理页面点击刷新按钮
- 重新加载页面
- 检查浏览器控制台是否有错误信息

### 样式显示异常
- 确保 `popup.css` 文件完整
- 检查浏览器是否支持 CSS 变量
- 尝试重启浏览器

## 📱 兼容性

- **Chrome**: 88+ (推荐)
- **Edge**: 88+ (基于 Chromium)
- **其他 Chromium 浏览器**: 理论支持

## 🔄 更新扩展

1. 替换文件夹中的文件
2. 在扩展管理页面点击刷新按钮
3. 重新加载使用扩展的页面

## 🗑️ 卸载扩展

1. 打开 `chrome://extensions/`
2. 找到 XPath 测试器
3. 点击"移除"按钮
4. 确认卸载

---

**安装完成后，享受使用 XPath 测试器！** 🎉
