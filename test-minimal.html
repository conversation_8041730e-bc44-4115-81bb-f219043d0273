<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>最小测试</title>
    <style>
        body { font-family: system-ui; margin: 40px; }
        .status { background: #f0f0f0; padding: 20px; margin: 20px 0; border-radius: 8px; }
        button { padding: 10px 20px; margin: 10px; background: #007AFF; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>XPath测试器 - 最小测试</h1>

    <div class="status" id="status">等待检查...</div>

    <button onclick="checkContentScript()">检查Content Script</button>
    <button onclick="manualLoad()">手动加载</button>
    <button onclick="forceInitialize()">强制初始化</button>
    <button onclick="testFunction()">测试函数</button>

    <div id="test-element">
        <p>测试元素</p>
        <span>内联元素</span>
    </div>

    <script>
        function log(message) {
            document.getElementById('status').innerHTML += message + '<br>';
            console.log(message);
        }

        function checkContentScript() {
            document.getElementById('status').innerHTML = '';
            log('🔍 检查Content Script状态...');

            // 检查脚本是否加载
            const scriptLoaded = window.xpathSidebarInitialized !== undefined;
            log(`脚本加载: ${scriptLoaded ? '✅' : '❌'}`);

            // 检查DOM元素
            const sidebar = document.getElementById('xpath-sidebar');
            log(`侧边栏元素: ${sidebar ? '✅' : '❌'}`);

            // 检查函数
            const hasToggle = typeof window.toggleSidebar === 'function';
            log(`toggleSidebar函数: ${hasToggle ? '✅' : '❌'}`);

            // 检查实例
            const hasInstance = !!window.xpathSidebarInstance;
            log(`实例对象: ${hasInstance ? '✅' : '❌'}`);

            // 列出所有xpath相关的全局变量
            const xpathVars = Object.keys(window).filter(key =>
                key.toLowerCase().includes('xpath') ||
                key.toLowerCase().includes('sidebar') ||
                key.toLowerCase().includes('toggle')
            );
            log(`相关变量: ${xpathVars.length > 0 ? xpathVars.join(', ') : '无'}`);
        }

        function manualLoad() {
            log('🔄 手动加载Content Script...');

            // 加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'sidebar.css';
            document.head.appendChild(link);

            // 加载JS
            const script = document.createElement('script');
            script.src = 'content-test.js';
            script.onload = () => {
                log('✅ 脚本加载完成');
                setTimeout(checkContentScript, 500);
            };
            script.onerror = () => {
                log('❌ 脚本加载失败');
            };
            document.head.appendChild(script);
        }

        function forceInitialize() {
            log('🔄 强制完成初始化...');

            try {
                // 如果初始化函数存在，调用它
                if (typeof window.initializeXPathSidebar === 'function') {
                    window.initializeXPathSidebar();
                    log('✅ 调用了 initializeXPathSidebar()');
                } else {
                    log('❌ initializeXPathSidebar 函数不存在');
                }

                // 手动设置标志
                window.xpathSidebarInitialized = true;
                log('✅ 设置初始化标志');

                // 手动创建实例对象
                if (!window.xpathSidebarInstance && typeof window.toggleSidebar === 'function') {
                    window.xpathSidebarInstance = {
                        toggleSidebar: window.toggleSidebar,
                        showSidebar: window.showSidebar,
                        hideSidebar: window.hideSidebar
                    };
                    log('✅ 手动创建实例对象');
                }

                setTimeout(checkContentScript, 500);

            } catch (error) {
                log(`❌ 强制初始化失败: ${error.message}`);
            }
        }

        function testFunction() {
            log('🧪 测试函数调用...');

            if (typeof window.toggleSidebar === 'function') {
                try {
                    window.toggleSidebar();
                    log('✅ toggleSidebar() 调用成功');
                } catch (error) {
                    log(`❌ 调用失败: ${error.message}`);
                }
            } else if (window.xpathSidebarInstance) {
                try {
                    window.xpathSidebarInstance.toggleSidebar();
                    log('✅ 实例方法调用成功');
                } catch (error) {
                    log(`❌ 实例调用失败: ${error.message}`);
                }
            } else {
                log('❌ 没有可用的函数');
            }
        }

        // 页面加载后自动检查
        window.addEventListener('load', () => {
            setTimeout(checkContentScript, 1000);
        });
    </script>
</body>
</html>
