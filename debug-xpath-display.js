// 调试XPath显示问题
// 在浏览器控制台中运行此脚本

(function() {
    console.log('🔍 调试XPath显示问题...');
    
    // 检查1: 侧边栏是否存在
    console.log('\n1️⃣ 检查侧边栏状态:');
    const sidebar = document.getElementById('xpath-sidebar');
    console.log('  侧边栏存在:', !!sidebar);
    console.log('  侧边栏可见:', window.sidebarVisible);
    
    if (!sidebar) {
        console.log('❌ 侧边栏不存在，尝试创建...');
        if (typeof window.showSidebar === 'function') {
            window.showSidebar();
            setTimeout(() => {
                console.log('侧边栏创建后，请重新运行此脚本');
            }, 500);
            return;
        }
    }
    
    // 检查2: 关键DOM元素
    console.log('\n2️⃣ 检查关键DOM元素:');
    const elements = {
        'xpath-input': document.getElementById('xpath-input'),
        'xpath-results-container': document.getElementById('xpath-results-container'),
        'xpath-results-count': document.getElementById('xpath-results-count')
    };
    
    Object.entries(elements).forEach(([id, element]) => {
        console.log(`  ${id}: ${element ? '✅' : '❌'}`);
        if (element) {
            console.log(`    HTML: ${element.outerHTML.substring(0, 100)}...`);
        }
    });
    
    // 检查3: 关键函数
    console.log('\n3️⃣ 检查关键函数:');
    const functions = [
        'getXPath', 'updateXPathInput', 'executeXPath', 'updateResults'
    ];
    
    functions.forEach(func => {
        const exists = typeof window[func] === 'function';
        console.log(`  ${func}: ${exists ? '✅' : '❌'}`);
    });
    
    // 检查4: 手动测试XPath生成
    console.log('\n4️⃣ 手动测试XPath生成:');
    if (typeof window.getXPath === 'function') {
        const testElement = document.body;
        const xpath = window.getXPath(testElement);
        console.log('  测试元素:', testElement.tagName);
        console.log('  生成的XPath:', xpath);
        
        // 测试XPath输入更新
        if (typeof window.updateXPathInput === 'function') {
            console.log('  尝试更新XPath输入...');
            window.updateXPathInput(xpath);
            
            // 检查输入框是否更新
            const input = document.getElementById('xpath-input');
            if (input) {
                console.log('  输入框值:', input.value);
                console.log('  输入框更新:', input.value === xpath ? '✅' : '❌');
            }
        }
    }
    
    // 检查5: 手动测试结果显示
    console.log('\n5️⃣ 手动测试结果显示:');
    if (typeof window.updateResults === 'function') {
        const testResults = [
            {
                text: '测试结果1',
                tagName: 'DIV',
                element: document.body
            },
            {
                text: '测试结果2',
                tagName: 'SPAN',
                element: document.head
            }
        ];
        
        console.log('  尝试更新结果显示...');
        window.updateResults(testResults);
        
        // 检查结果是否显示
        const container = document.getElementById('xpath-results-container');
        const count = document.getElementById('xpath-results-count');
        
        if (container && count) {
            console.log('  结果容器子元素数量:', container.children.length);
            console.log('  计数显示:', count.textContent);
            console.log('  结果显示:', container.children.length === testResults.length ? '✅' : '❌');
        }
    }
    
    // 检查6: 事件监听器测试
    console.log('\n6️⃣ 测试Shift+点击事件:');
    console.log('  请按住Shift键并点击页面上的任意元素');
    console.log('  观察控制台输出...');
    
    // 添加临时事件监听器来调试
    let tempClickHandler = function(e) {
        if (e.shiftKey) {
            console.log('🎯 临时调试 - Shift+点击检测到:', {
                target: e.target.tagName,
                sidebarVisible: window.sidebarVisible,
                hasSidebar: !!window.sidebar,
                targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false
            });
            
            if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                console.log('🎯 条件满足，开始处理...');
                
                // 生成XPath
                if (typeof window.getXPath === 'function') {
                    const xpath = window.getXPath(e.target);
                    console.log('🎯 XPath生成:', xpath);
                    
                    // 更新输入框
                    if (typeof window.updateXPathInput === 'function') {
                        console.log('🎯 更新输入框...');
                        window.updateXPathInput(xpath);
                        
                        // 检查更新结果
                        setTimeout(() => {
                            const input = document.getElementById('xpath-input');
                            const container = document.getElementById('xpath-results-container');
                            const count = document.getElementById('xpath-results-count');
                            
                            console.log('🎯 更新后状态:');
                            console.log('  输入框值:', input ? input.value : '未找到');
                            console.log('  结果数量:', count ? count.textContent : '未找到');
                            console.log('  结果容器子元素:', container ? container.children.length : '未找到');
                        }, 100);
                    }
                }
            } else {
                console.log('🎯 条件不满足，跳过处理');
            }
        }
    };
    
    document.addEventListener('click', tempClickHandler);
    
    // 10秒后移除临时监听器
    setTimeout(() => {
        document.removeEventListener('click', tempClickHandler);
        console.log('🎯 临时调试监听器已移除');
    }, 10000);
    
    console.log('\n✅ 调试脚本运行完成！');
    console.log('现在请按住Shift键并点击页面元素进行测试');
    
})();
