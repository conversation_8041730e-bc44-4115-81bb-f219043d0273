<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接加载测试</title>
    <link rel="stylesheet" href="sidebar.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .test-element {
            padding: 20px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007AFF;
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
        }
        .controls {
            position: fixed;
            top: 60px;
            right: 10px;
            background: white;
            padding: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .controls button {
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 8px 12px;
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status" id="status">准备加载...</div>
    
    <div class="controls">
        <button onclick="loadContentScript()">加载Content Script</button>
        <button onclick="testToggle()">测试切换</button>
        <button onclick="checkStatus()">检查状态</button>
        <button onclick="clearConsole()">清空控制台</button>
    </div>
    
    <h1>XPath测试器 - 直接加载测试</h1>
    
    <div class="test-element" id="test-1">
        <h2>测试元素 1</h2>
        <p>这是第一个测试元素，用于验证XPath生成。</p>
        <button>测试按钮</button>
    </div>
    
    <div class="test-element" class="special-class">
        <h2>测试元素 2</h2>
        <p>这是第二个测试元素，带有特殊class。</p>
        <ul>
            <li>列表项 1</li>
            <li>列表项 2</li>
            <li>列表项 3</li>
        </ul>
    </div>

    <script>
        let statusEl = document.getElementById('status');
        
        function updateStatus(message, color = '#007AFF') {
            statusEl.textContent = message;
            statusEl.style.background = color;
            console.log('Status:', message);
        }
        
        function loadContentScript() {
            updateStatus('正在加载...', '#FF9500');
            
            // 直接加载content script
            const script = document.createElement('script');
            script.src = 'content.js';
            script.onload = () => {
                updateStatus('脚本已加载', '#34C759');
                setTimeout(checkStatus, 500);
            };
            script.onerror = () => {
                updateStatus('加载失败', '#FF3B30');
            };
            document.head.appendChild(script);
        }
        
        function testToggle() {
            console.log('Testing toggle function...');
            if (typeof window.toggleSidebar === 'function') {
                window.toggleSidebar();
                updateStatus('切换成功', '#34C759');
            } else {
                updateStatus('函数不存在', '#FF3B30');
                console.log('Available functions:', Object.keys(window).filter(key => key.includes('xpath') || key.includes('Sidebar') || key.includes('toggle')));
            }
        }
        
        function checkStatus() {
            const sidebar = document.getElementById('xpath-sidebar');
            const initialized = window.xpathSidebarInitialized;
            const hasToggle = typeof window.toggleSidebar === 'function';
            const hasInstance = !!window.xpathSidebarInstance;
            
            console.log('Status check:', {
                sidebar: !!sidebar,
                initialized,
                hasToggle,
                hasInstance
            });
            
            if (sidebar && initialized && hasToggle) {
                updateStatus('✅ 完全加载', '#34C759');
            } else if (sidebar) {
                updateStatus('⚠️ 部分加载', '#FF9500');
            } else if (hasToggle) {
                updateStatus('🔧 函数存在', '#FF9500');
            } else {
                updateStatus('❌ 未加载', '#FF3B30');
            }
        }
        
        function clearConsole() {
            console.clear();
            updateStatus('控制台已清空', '#007AFF');
        }
        
        // 页面加载完成后自动加载content script
        window.addEventListener('load', () => {
            console.log('Page loaded, auto-loading content script...');
            setTimeout(loadContentScript, 500);
        });
        
        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            updateStatus('🎉 侧边栏已创建', '#34C759');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
