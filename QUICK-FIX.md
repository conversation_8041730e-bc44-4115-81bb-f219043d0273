# XPath 测试器 - 快速修复指南

## 🚨 常见问题解决

### 问题1: "Could not establish connection" 错误

**症状：**
- 扩展程序页面显示此错误
- 点击扩展图标没有反应
- Background script 报错

**原因：** Background script 无法与 content script 通信

### 问题2: Content Script 未加载

**症状：**
- 调试页面显示所有功能都不存在
- 侧边栏不出现
- 函数未定义

### 🔧 立即解决方案

#### 方法 1: 独立测试（推荐）

1. **打开独立测试页面**
   ```
   打开 standalone-test.html 文件
   ```

2. **加载脚本**
   ```
   点击"加载脚本"按钮
   观察日志输出
   ```

3. **验证功能**
   ```
   点击"检查状态"按钮
   如果显示✅，说明脚本正常工作
   点击"测试切换"验证侧边栏功能
   ```

4. **如果仍然失败**
   ```
   启动本地服务器测试:
   python3 start-server.py
   访问: http://localhost:8000/standalone-test.html
   ```

#### 方法 2: 浏览器控制台修复

1. **打开任意网页**
2. **按 F12 打开开发者工具**
3. **在控制台中粘贴以下代码**：

```javascript
// 手动注入XPath测试器
(async function() {
    console.log('🔧 手动注入XPath测试器...');

    // 注入CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = chrome.runtime.getURL('sidebar.css');
    document.head.appendChild(link);

    // 注入JS
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('content.js');
    script.onload = () => {
        console.log('✅ XPath测试器加载完成');
        setTimeout(() => {
            if (typeof window.toggleSidebar === 'function') {
                console.log('🎉 可以使用 window.toggleSidebar() 切换侧边栏');
            }
        }, 1000);
    };
    document.head.appendChild(script);
})();
```

4. **等待加载完成**
5. **测试功能**：
   ```javascript
   // 切换侧边栏
   window.toggleSidebar();
   ```

#### 方法 3: 扩展重新安装

如果以上方法都不行：

1. **完全卸载扩展**
   ```
   1. 打开 chrome://extensions/
   2. 找到 XPath测试器
   3. 点击"移除"
   ```

2. **重启浏览器**

3. **重新安装扩展**
   ```
   1. 重新加载扩展页面
   2. 点击"加载已解压的扩展程序"
   3. 选择项目文件夹
   ```

4. **验证安装**
   ```
   打开 test-minimal.html 测试
   ```

### 🎯 验证成功标志

扩展正常工作时，你应该看到：

1. **test-minimal.html 页面**：
   - 脚本加载: ✅
   - 侧边栏元素: ✅
   - toggleSidebar函数: ✅
   - 实例对象: ✅

2. **实际网页**：
   - 点击扩展图标，侧边栏从右侧滑出
   - 按住Shift键悬停元素显示蓝色高亮
   - 点击元素自动生成XPath

### 🐛 仍然有问题？

1. **检查浏览器版本**
   - 确保使用 Chrome 88+ 或 Edge 88+

2. **检查文件完整性**
   - 确保所有文件都在项目文件夹中
   - 特别检查 content.js 和 sidebar.css

3. **查看详细错误**
   - 打开 chrome://extensions/
   - 点击扩展的"详细信息"
   - 查看"错误"部分

4. **联系支持**
   - 提供浏览器版本信息
   - 提供控制台错误截图
   - 描述具体的操作步骤

---

**大多数问题都可以通过方法1或方法2解决！**
