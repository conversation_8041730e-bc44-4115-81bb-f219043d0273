<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shift+点击功能测试</title>
    <link rel="stylesheet" href="sidebar.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-area {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-element {
            padding: 15px;
            margin: 10px 0;
            background: #f0f0f0;
            border-radius: 8px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .test-element:hover {
            border-color: #007AFF;
            background: #f8f9ff;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
        }
        .btn:hover {
            background: #0056CC;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }
        .success { background: #34C759; color: white; }
        .error { background: #FF3B30; color: white; }
        .warning { background: #FF9500; color: white; }
        .info { background: #007AFF; color: white; }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .shift-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #FF9500;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 600;
            display: none;
            z-index: 1000;
        }
        .shift-indicator.active {
            display: block;
            background: #34C759;
        }
    </style>
</head>
<body>
    <div class="shift-indicator" id="shiftIndicator">Shift键已按下 - 可以点击元素</div>
    
    <div class="header">
        <h1>🎯 Shift+点击功能测试</h1>
        <p>测试XPath生成的Shift+点击功能</p>
    </div>

    <div class="controls">
        <h2>🎮 控制面板</h2>
        <button class="btn" onclick="loadScript()">1. 加载脚本</button>
        <button class="btn" onclick="showSidebar()">2. 显示侧边栏</button>
        <button class="btn" onclick="testShiftClick()">3. 测试Shift+点击</button>
        <button class="btn" onclick="clearLog()">清空日志</button>
        
        <div id="status-display" class="status info">等待操作...</div>
    </div>

    <div class="controls">
        <h2>📋 实时日志</h2>
        <div id="log-output" class="log">等待日志...</div>
    </div>

    <div class="test-area">
        <h2>🧪 测试元素区域</h2>
        <p><strong>使用说明：</strong></p>
        <ol>
            <li>先点击"加载脚本"和"显示侧边栏"</li>
            <li>按住 <kbd>Shift</kbd> 键</li>
            <li>将鼠标悬停在下面的元素上（应该显示蓝色高亮）</li>
            <li>点击元素（应该生成XPath并显示在侧边栏中）</li>
        </ol>
        
        <div class="test-element" id="element-1" data-test="first">
            <h3>测试元素 1</h3>
            <p>ID: element-1, data-test: first</p>
        </div>
        
        <div class="test-element" class="special-class" data-test="second">
            <h3>测试元素 2</h3>
            <p>Class: special-class, data-test: second</p>
            <button onclick="alert('普通点击')">普通按钮</button>
        </div>
        
        <div class="test-element" data-test="third">
            <h3>测试元素 3</h3>
            <ul>
                <li>列表项 1</li>
                <li>列表项 2</li>
                <li>列表项 3</li>
            </ul>
        </div>
        
        <div class="test-element">
            <h3>测试元素 4</h3>
            <p>没有ID和特殊属性的元素</p>
            <span>内联元素</span>
        </div>
    </div>

    <script>
        let statusDisplay = document.getElementById('status-display');
        let logOutput = document.getElementById('log-output');
        let shiftIndicator = document.getElementById('shiftIndicator');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            logOutput.textContent += logMessage + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(logMessage);
        }
        
        function updateStatus(message, type = 'info') {
            statusDisplay.className = `status ${type}`;
            statusDisplay.textContent = message;
            log(message, type);
        }
        
        function loadScript() {
            updateStatus('正在加载脚本...', 'info');
            
            // 加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'sidebar.css';
            document.head.appendChild(link);
            
            // 加载JS
            const script = document.createElement('script');
            script.src = 'content-test.js';
            
            script.onload = () => {
                updateStatus('脚本加载成功！', 'success');
                log('✅ 所有脚本已加载');
                
                // 检查函数是否可用
                setTimeout(() => {
                    if (typeof window.toggleSidebar === 'function') {
                        log('✅ toggleSidebar 函数可用');
                    }
                    if (typeof window.setupEventListeners === 'function') {
                        log('✅ setupEventListeners 函数可用');
                    }
                }, 100);
            };
            
            script.onerror = () => {
                updateStatus('脚本加载失败！', 'error');
            };
            
            document.head.appendChild(script);
        }
        
        function showSidebar() {
            if (typeof window.showSidebar === 'function') {
                window.showSidebar();
                updateStatus('侧边栏已显示', 'success');
                log('✅ 侧边栏已显示，现在可以测试Shift+点击');
            } else {
                updateStatus('请先加载脚本', 'error');
            }
        }
        
        function testShiftClick() {
            log('🧪 开始测试Shift+点击功能...');
            log('📝 请按住Shift键并点击测试元素');
            updateStatus('请按住Shift键并点击测试元素', 'warning');
        }
        
        function clearLog() {
            logOutput.textContent = '日志已清空...\n';
            updateStatus('日志已清空', 'info');
        }
        
        // 监听Shift键状态
        let shiftPressed = false;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && !shiftPressed) {
                shiftPressed = true;
                shiftIndicator.classList.add('active');
                log('⌨️ Shift键已按下 - 十字光标模式');
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                shiftPressed = false;
                shiftIndicator.classList.remove('active');
                log('⌨️ Shift键已释放');
            }
        });
        
        // 监听鼠标事件
        document.addEventListener('mouseover', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                log(`🖱️ Shift+悬停: ${e.target.tagName} (${e.target.textContent.substring(0, 30)}...)`);
            }
        });
        
        document.addEventListener('click', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                const element = e.target.closest('.test-element');
                log(`🎯 Shift+点击: ${element.querySelector('h3').textContent}`);
                
                // 检查XPath是否生成
                setTimeout(() => {
                    const xpathInput = document.getElementById('xpath-input');
                    if (xpathInput && xpathInput.value) {
                        log(`✅ XPath已生成: ${xpathInput.value}`);
                        updateStatus('XPath生成成功！', 'success');
                    } else {
                        log('❌ XPath未生成');
                        updateStatus('XPath生成失败', 'error');
                    }
                }, 100);
            }
        });
        
        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            log('🎉 检测到侧边栏被创建！');
                        }
                        if (node.className === 'xpath-highlight-overlay') {
                            log('🎯 检测到高亮覆盖层被创建！');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 页面加载完成
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            updateStatus('页面已准备就绪，请按顺序点击按钮', 'info');
        });
    </script>
</body>
</html>
