// 安全的消息发送函数
async function sendMessageSafely(tabId, message, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await chrome.tabs.sendMessage(tabId, message);
      return { success: true, response };
    } catch (error) {
      console.log(`📤 Message attempt ${i + 1} failed:`, error.message);
      if (i < retries - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }
  return { success: false };
}

// 直接执行函数（不依赖消息传递）
async function executeDirectly(tabId, action) {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: (actionType) => {
        console.log('🔧 Direct execution for action:', actionType);

        // 确保初始化
        if (!window.xpathSidebarInitialized && typeof window.initializeXPathSidebar === 'function') {
          window.initializeXPathSidebar();
        }

        // 执行对应操作
        switch (actionType) {
          case 'toggleSidebar':
            if (typeof window.toggleSidebar === 'function') {
              window.toggleSidebar();
              return '✅ toggleSidebar executed';
            }
            break;
          case 'showSidebar':
            if (typeof window.showSidebar === 'function') {
              window.showSidebar();
              return '✅ showSidebar executed';
            }
            break;
          case 'hideSidebar':
            if (typeof window.hideSidebar === 'function') {
              window.hideSidebar();
              return '✅ hideSidebar executed';
            }
            break;
        }

        return '❌ Function not available';
      },
      args: [action]
    });

    return results[0].result;
  } catch (error) {
    console.error('❌ Direct execution failed:', error);
    return '❌ Execution failed';
  }
}

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  console.log('🎯 Extension icon clicked for tab:', tab.id, 'URL:', tab.url);

  // 检查是否是特殊页面
  if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
      tab.url.startsWith('moz-extension://') || tab.url.startsWith('edge://')) {
    console.log('❌ Cannot inject scripts into special pages:', tab.url);
    return;
  }

  // 首先检查content script是否已经加载
  console.log('🔍 Checking if content script is loaded...');
  let scriptLoaded = false;

  try {
    const checkResults = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        return {
          loaded: !!window.xpathSidebarInitialized,
          hasToggle: typeof window.toggleSidebar === 'function'
        };
      }
    });

    scriptLoaded = checkResults[0].result.loaded && checkResults[0].result.hasToggle;
    console.log('📊 Script status:', checkResults[0].result);
  } catch (error) {
    console.log('⚠️ Cannot check script status, assuming not loaded');
  }

  if (scriptLoaded) {
    console.log('✅ Script already loaded, executing directly...');
    const directResult = await executeDirectly(tab.id, 'toggleSidebar');
    if (directResult.includes('✅')) {
      console.log('✅ Direct execution successful:', directResult);
      return;
    }
  }

  console.log('📥 Script not loaded or failed, injecting scripts...');

  try {
    // 注入脚本
    await chrome.scripting.insertCSS({
      target: { tabId: tab.id },
      files: ['sidebar.css']
    });

    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['content-test.js']
    });

    console.log('✅ Scripts injected');

    // 等待初始化并强制设置
    await new Promise(resolve => setTimeout(resolve, 500));

    // 强制初始化和设置
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: () => {
        // 强制初始化
        if (typeof window.initializeXPathSidebar === 'function') {
          window.initializeXPathSidebar();
        }

        // 确保全局函数可用
        if (typeof window.toggleSidebar === 'function') {
          console.log('✅ toggleSidebar function is available');
          return 'Functions ready';
        } else {
          console.log('❌ toggleSidebar function not found');
          return 'Functions not ready';
        }
      }
    });

    // 再次尝试直接执行
    const retryResult = await executeDirectly(tab.id, 'toggleSidebar');
    console.log('🔄 Retry result:', retryResult);

    // 最后尝试消息传递（可选）
    if (!retryResult.includes('✅')) {
      console.log('📤 Trying message passing as last resort...');
      const messageResult = await sendMessageSafely(tab.id, { action: 'toggleSidebar' }, 1);
      if (messageResult.success) {
        console.log('✅ Message sent successfully:', messageResult.response);
      } else {
        console.log('❌ All methods failed');
      }
    }

  } catch (error) {
    console.error('❌ Script injection failed:', error);
  }
});
