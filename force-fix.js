// 强制修复XPath扩展问题
// 在浏览器控制台中运行此脚本来强制修复关闭按钮和Shift功能

(function() {
    console.log('🔧 开始强制修复XPath扩展...');
    
    // 修复1: 强制设置关闭按钮事件
    function fixCloseButton() {
        console.log('\n🔴 修复关闭按钮...');
        
        const closeBtn = document.getElementById('xpath-close-btn');
        if (closeBtn) {
            // 移除所有现有的事件监听器
            const newCloseBtn = closeBtn.cloneNode(true);
            closeBtn.parentNode.replaceChild(newCloseBtn, closeBtn);
            
            // 添加新的事件监听器
            newCloseBtn.addEventListener('click', function(e) {
                console.log('🔴 关闭按钮点击 - 强制修复版本');
                e.preventDefault();
                e.stopPropagation();
                
                // 隐藏侧边栏
                if (typeof window.hideSidebar === 'function') {
                    window.hideSidebar();
                } else {
                    // 手动隐藏
                    window.sidebarVisible = false;
                    const sidebar = document.getElementById('xpath-sidebar');
                    if (sidebar) {
                        sidebar.classList.remove('xpath-sidebar-visible');
                    }
                    document.body.classList.remove('xpath-sidebar-open');
                    document.body.style.cursor = 'default';
                    console.log('✅ 侧边栏已手动隐藏');
                }
            });
            
            console.log('✅ 关闭按钮事件已修复');
            return true;
        } else {
            console.log('❌ 关闭按钮不存在');
            return false;
        }
    }
    
    // 修复2: 强制设置Clear按钮事件
    function fixClearButton() {
        console.log('\n🧹 修复Clear按钮...');
        
        const clearBtn = document.getElementById('xpath-clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', function(e) {
                console.log('🧹 Clear按钮点击');
                e.preventDefault();
                e.stopPropagation();
                
                const input = document.getElementById('xpath-input');
                if (input) {
                    input.value = '';
                    console.log('✅ XPath已清除');
                }
                
                // 清除高亮
                const overlays = document.querySelectorAll('.xpath-highlight-overlay');
                overlays.forEach(overlay => overlay.remove());
                
                // 清除结果
                const resultsContainer = document.getElementById('xpath-results-container');
                if (resultsContainer) {
                    resultsContainer.innerHTML = '';
                }
                
                const countElement = document.getElementById('xpath-results-count');
                if (countElement) {
                    countElement.textContent = '0 items';
                }
            });
            
            console.log('✅ Clear按钮事件已修复');
            return true;
        } else {
            console.log('❌ Clear按钮不存在');
            return false;
        }
    }
    
    // 修复3: 强制设置Copy按钮事件
    function fixCopyButton() {
        console.log('\n📋 修复Copy按钮...');
        
        const copyBtn = document.getElementById('xpath-copy-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', function(e) {
                console.log('📋 Copy按钮点击');
                e.preventDefault();
                e.stopPropagation();
                
                const input = document.getElementById('xpath-input');
                if (input && input.value) {
                    navigator.clipboard.writeText(input.value).then(() => {
                        console.log('✅ XPath已复制到剪贴板');
                    }).catch(err => {
                        console.error('❌ 复制失败:', err);
                    });
                }
            });
            
            console.log('✅ Copy按钮事件已修复');
            return true;
        } else {
            console.log('❌ Copy按钮不存在');
            return false;
        }
    }
    
    // 修复4: 强制设置Shift功能
    function fixShiftFunctionality() {
        console.log('\n⌨️ 修复Shift功能...');
        
        // 移除现有的事件监听器（如果有的话）
        document.removeEventListener('keydown', window.shiftKeyDownHandler);
        document.removeEventListener('keyup', window.shiftKeyUpHandler);
        document.removeEventListener('mouseover', window.shiftMouseOverHandler);
        document.removeEventListener('click', window.shiftClickHandler);
        
        // 添加新的事件监听器
        window.shiftKeyDownHandler = function(e) {
            if (e.key === 'Shift' && window.sidebarVisible) {
                document.body.style.cursor = 'crosshair';
                console.log('🎯 Shift键按下 - 十字光标已启用');
            }
        };
        
        window.shiftKeyUpHandler = function(e) {
            if (e.key === 'Shift') {
                document.body.style.cursor = 'default';
                // 移除高亮
                const overlays = document.querySelectorAll('.xpath-highlight-overlay');
                overlays.forEach(overlay => overlay.remove());
                console.log('🎯 Shift键释放 - 光标已重置');
            }
        };
        
        window.shiftMouseOverHandler = function(e) {
            if (e.shiftKey && window.sidebarVisible) {
                const sidebar = document.getElementById('xpath-sidebar');
                if (sidebar && !sidebar.contains(e.target)) {
                    // 移除现有高亮
                    const overlays = document.querySelectorAll('.xpath-highlight-overlay');
                    overlays.forEach(overlay => overlay.remove());
                    
                    // 创建新高亮
                    const rect = e.target.getBoundingClientRect();
                    const overlay = document.createElement('div');
                    overlay.className = 'xpath-highlight-overlay';
                    overlay.style.cssText = 
                        'position: fixed !important;' +
                        'top: ' + rect.top + 'px !important;' +
                        'left: ' + rect.left + 'px !important;' +
                        'width: ' + rect.width + 'px !important;' +
                        'height: ' + rect.height + 'px !important;';
                    
                    document.body.appendChild(overlay);
                    console.log('🎯 元素已高亮:', e.target.tagName);
                }
            }
        };
        
        window.shiftClickHandler = function(e) {
            if (e.shiftKey && window.sidebarVisible) {
                const sidebar = document.getElementById('xpath-sidebar');
                if (sidebar && !sidebar.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 生成XPath
                    let xpath = '';
                    if (e.target.id) {
                        xpath = '//*[@id="' + e.target.id + '"]';
                    } else {
                        let element = e.target;
                        let path = '';
                        while (element && element.nodeType === Node.ELEMENT_NODE) {
                            let selector = element.nodeName.toLowerCase();
                            if (element.className) {
                                selector += '[@class="' + element.className + '"]';
                            }
                            path = '/' + selector + path;
                            element = element.parentNode;
                        }
                        xpath = path;
                    }
                    
                    // 更新输入框
                    const input = document.getElementById('xpath-input');
                    if (input) {
                        input.value = xpath;
                        console.log('✅ XPath已生成:', xpath);
                        
                        // 执行XPath查询
                        try {
                            const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
                            const elements = [];
                            
                            for (let i = 0; i < result.snapshotLength; i++) {
                                const element = result.snapshotItem(i);
                                elements.push({
                                    text: element.textContent.trim().substring(0, 100) || '[无文本]',
                                    tagName: element.tagName,
                                    element: element
                                });
                            }
                            
                            // 更新结果显示
                            const container = document.getElementById('xpath-results-container');
                            const countElement = document.getElementById('xpath-results-count');
                            
                            if (container && countElement) {
                                countElement.textContent = elements.length + ' items';
                                container.innerHTML = '';
                                
                                elements.forEach((item, index) => {
                                    const resultItem = document.createElement('div');
                                    resultItem.className = 'xpath-result-item';
                                    resultItem.innerHTML =
                                        '<div class="xpath-result-tag">' + (item.tagName || 'TEXT') + '</div>' +
                                        '<div class="xpath-result-text">' + item.text + '</div>';
                                    container.appendChild(resultItem);
                                });
                                
                                console.log('✅ 结果已更新:', elements.length, '个元素');
                            }
                        } catch (error) {
                            console.error('❌ XPath执行失败:', error);
                        }
                    }
                }
            }
        };
        
        // 添加事件监听器
        document.addEventListener('keydown', window.shiftKeyDownHandler);
        document.addEventListener('keyup', window.shiftKeyUpHandler);
        document.addEventListener('mouseover', window.shiftMouseOverHandler);
        document.addEventListener('click', window.shiftClickHandler);
        
        console.log('✅ Shift功能已修复');
    }
    
    // 执行所有修复
    console.log('\n🚀 开始执行修复...');
    
    // 确保侧边栏存在
    if (!document.getElementById('xpath-sidebar')) {
        console.log('侧边栏不存在，尝试创建...');
        if (typeof window.showSidebar === 'function') {
            window.showSidebar();
        }
    }
    
    // 等待DOM更新后执行修复
    setTimeout(() => {
        const results = {
            closeButton: fixCloseButton(),
            clearButton: fixClearButton(),
            copyButton: fixCopyButton()
        };
        
        fixShiftFunctionality();
        
        console.log('\n📊 修复结果:');
        Object.entries(results).forEach(([key, success]) => {
            console.log(`  ${key}: ${success ? '✅' : '❌'}`);
        });
        
        console.log('\n🎉 强制修复完成！');
        console.log('现在请测试:');
        console.log('  1. 点击关闭按钮（×）');
        console.log('  2. 点击Clear按钮（橙色）');
        console.log('  3. 点击Copy按钮（绿色）');
        console.log('  4. 按住Shift键并点击页面元素');
        
    }, 100);
    
})();
