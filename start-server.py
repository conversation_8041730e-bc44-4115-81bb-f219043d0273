#!/usr/bin/env python3
"""
简单的HTTP服务器，用于测试XPath扩展
在项目目录中运行: python3 start-server.py
然后访问: http://localhost:8000/standalone-test.html
"""

import http.server
import socketserver
import os
import sys

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头，允许扩展访问
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

def main():
    # 确保在正确的目录中
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print(f"🚀 启动HTTP服务器...")
    print(f"📁 服务目录: {script_dir}")
    print(f"🌐 访问地址: http://localhost:{PORT}")
    print(f"🧪 测试页面: http://localhost:{PORT}/standalone-test.html")
    print(f"📄 最终测试: http://localhost:{PORT}/test-final.html")
    print(f"⚠️  按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
