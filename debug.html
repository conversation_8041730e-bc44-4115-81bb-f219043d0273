<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath测试器 - 调试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .debug-info {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007AFF;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .element-test {
            padding: 15px;
            margin: 10px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        <h1>XPath测试器调试页面</h1>
        <p><strong>使用说明：</strong></p>
        <ol>
            <li>打开浏览器开发者工具（F12）查看控制台</li>
            <li>点击浏览器工具栏中的XPath测试器图标</li>
            <li>查看控制台输出的调试信息</li>
            <li>如果侧边栏出现，测试Shift+点击功能</li>
        </ol>
    </div>

    <div class="debug-info">
        <h2>测试元素</h2>
        <button class="test-button" id="test-btn-1">测试按钮 1</button>
        <button class="test-button" id="test-btn-2">测试按钮 2</button>

        <div class="element-test" data-test="example">
            <h3>测试标题</h3>
            <p>这是一个测试段落，用于验证XPath生成功能。</p>
            <ul>
                <li class="list-item">列表项 1</li>
                <li class="list-item">列表项 2</li>
                <li class="list-item">列表项 3</li>
            </ul>
        </div>
    </div>

    <div class="debug-info">
        <h2>手动测试</h2>
        <button onclick="testExtensionStatus()" class="test-button">检查扩展状态</button>
        <button onclick="testContentScript()" class="test-button">测试Content Script</button>
        <button onclick="forceInitialize()" class="test-button">强制初始化</button>
        <button onclick="clearConsole()" class="test-button">清空控制台</button>

        <h3>控制台输出：</h3>
        <div class="console-output" id="console-output">
            等待测试结果...
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `[${timestamp}] ${message}<br>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(message);
        }

        function testExtensionStatus() {
            log('🔍 检查扩展状态...');

            // 检查是否有XPath侧边栏
            const sidebar = document.getElementById('xpath-sidebar');
            if (sidebar) {
                log('✅ 找到XPath侧边栏元素');
                log(`📊 侧边栏状态: ${sidebar.classList.contains('xpath-sidebar-visible') ? '显示' : '隐藏'}`);
            } else {
                log('❌ 未找到XPath侧边栏元素');
            }

            // 检查全局变量
            if (typeof window.xpathSidebarInitialized !== 'undefined') {
                log(`✅ 扩展已初始化: ${window.xpathSidebarInitialized}`);
            } else {
                log('❌ 扩展未初始化');
            }
        }

        function testContentScript() {
            log('🧪 测试Content Script功能...');

            // 检查全局函数
            const functions = ['toggleSidebar', 'showSidebar', 'hideSidebar', 'init'];
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ ${funcName} 函数存在`);
                } else {
                    log(`❌ ${funcName} 函数不存在`);
                }
            });

            // 检查全局对象
            if (window.xpathSidebarInstance) {
                log('✅ xpathSidebarInstance 对象存在');
                log(`📊 侧边栏状态: ${window.xpathSidebarInstance.sidebarVisible ? '显示' : '隐藏'}`);
            } else {
                log('❌ xpathSidebarInstance 对象不存在');
            }

            // 尝试调用函数
            try {
                if (typeof window.toggleSidebar === 'function') {
                    window.toggleSidebar();
                    log('📤 调用 toggleSidebar() 成功');
                } else if (window.xpathSidebarInstance && typeof window.xpathSidebarInstance.toggleSidebar === 'function') {
                    window.xpathSidebarInstance.toggleSidebar();
                    log('📤 通过实例调用 toggleSidebar() 成功');
                } else {
                    log('❌ 无法找到可用的 toggleSidebar 函数');
                }
            } catch (error) {
                log(`❌ 调用失败: ${error.message}`);
            }
        }

        function clearConsole() {
            consoleOutput.innerHTML = '控制台已清空...<br>';
        }

        function forceInitialize() {
            log('🔄 强制初始化XPath侧边栏...');

            try {
                // 创建并执行初始化脚本
                const script = document.createElement('script');
                script.textContent = `
                    (function() {
                        // 重置状态
                        window.xpathSidebarInitialized = false;

                        // 如果存在旧的侧边栏，移除它
                        const oldSidebar = document.getElementById('xpath-sidebar');
                        if (oldSidebar) {
                            oldSidebar.remove();
                            console.log('移除旧的侧边栏');
                        }

                        // 如果存在初始化函数，调用它
                        if (typeof initializeXPathSidebar === 'function') {
                            initializeXPathSidebar();
                            console.log('调用初始化函数');
                        } else if (typeof init === 'function') {
                            init();
                            console.log('调用init函数');
                        } else {
                            console.log('找不到初始化函数');
                        }
                    })();
                `;
                document.head.appendChild(script);
                document.head.removeChild(script);

                log('✅ 初始化脚本执行完成');

                // 检查结果
                setTimeout(() => {
                    testExtensionStatus();
                }, 500);

            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`);
            }
        }

        // 页面加载完成后的检查
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            setTimeout(() => {
                testExtensionStatus();
            }, 1000);
        });

        // 监听键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift') {
                log('⌨️ Shift键按下');
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                log('⌨️ Shift键释放');
            }
        });

        // 监听鼠标事件
        document.addEventListener('mouseover', (e) => {
            if (e.shiftKey) {
                log(`🖱️ Shift+悬停: ${e.target.tagName}`);
            }
        });

        document.addEventListener('click', (e) => {
            if (e.shiftKey) {
                log(`🖱️ Shift+点击: ${e.target.tagName}`);
            }
        });
    </script>
</body>
</html>
