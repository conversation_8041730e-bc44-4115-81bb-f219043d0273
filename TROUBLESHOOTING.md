# XPath 测试器 - 故障排除指南

## 🚨 常见问题

### 1. 点击扩展图标没有反应

**可能原因：**
- Content script 未正确加载
- 权限不足
- 页面加载未完成
- 消息传递失败

**解决方案：**

1. **检查扩展是否正确安装**
   ```
   1. 打开 chrome://extensions/
   2. 确保 XPath测试器 已启用
   3. 检查是否有错误信息
   4. 确保版本为 1.1.0 或更高
   ```

2. **重新加载扩展**
   ```
   1. 在扩展管理页面点击刷新按钮
   2. 重新加载当前网页（Ctrl+F5 强制刷新）
   3. 再次点击扩展图标
   ```

3. **检查控制台错误**
   ```
   1. 按 F12 打开开发者工具
   2. 切换到 Console 标签
   3. 点击扩展图标查看错误信息
   4. 查找 "Could not establish connection" 错误
   ```

4. **手动注入脚本（临时解决方案）**
   ```javascript
   // 在控制台中运行以下代码
   const script = document.createElement('script');
   script.src = chrome.runtime.getURL('content.js');
   document.head.appendChild(script);
   ```

### 2. 侧边栏不显示

**可能原因：**
- CSS 文件未加载
- DOM 元素创建失败
- 样式冲突

**解决方案：**

1. **检查 CSS 加载**
   ```
   1. 打开开发者工具
   2. 切换到 Elements 标签
   3. 查找 id="xpath-sidebar" 的元素
   ```

2. **手动测试**
   ```javascript
   // 在控制台中运行
   console.log('Sidebar element:', document.getElementById('xpath-sidebar'));
   console.log('Initialized:', window.xpathSidebarInitialized);
   ```

### 3. Shift+点击不工作

**可能原因：**
- 事件监听器未绑定
- 侧边栏未打开
- 页面元素被其他脚本拦截

**解决方案：**

1. **确保侧边栏已打开**
   - 先点击扩展图标打开侧边栏
   - 再尝试 Shift+点击功能

2. **检查事件监听**
   ```javascript
   // 在控制台中测试
   document.addEventListener('keydown', (e) => {
     if (e.key === 'Shift') console.log('Shift pressed');
   });
   ```

### 4. XPath 生成不准确

**可能原因：**
- 元素没有唯一标识
- DOM 结构复杂
- 动态生成的元素

**解决方案：**

1. **手动编辑 XPath**
   - 在侧边栏输入框中直接修改
   - 使用更具体的属性选择器

2. **常用 XPath 模式**
   ```xpath
   //*[@id="element-id"]           // 通过 ID 选择
   //div[@class="class-name"]      // 通过 class 选择
   //button[contains(text(),"文本")] // 通过文本内容选择
   ```

## 🚨 特定错误解决

### "Could not establish connection" 错误

这是最常见的错误，表示background script无法与content script通信。

**解决步骤：**

1. **使用独立测试页面**
   ```
   打开 standalone-test.html 页面
   点击"加载脚本"按钮
   点击"检查状态"查看结果
   如果所有项都显示❌，说明脚本文件有问题
   ```

2. **手动测试**
   ```javascript
   // 在控制台中检查
   console.log('Sidebar:', document.getElementById('xpath-sidebar'));
   console.log('Initialized:', window.xpathSidebarInitialized);
   console.log('Toggle function:', typeof window.toggleSidebar);
   ```

3. **手动加载测试**
   ```
   1. 打开 test-minimal.html 页面
   2. 点击"手动加载"按钮
   3. 等待加载完成后点击"检查Content Script"
   4. 如果成功，说明扩展文件正常，问题在于自动注入
   ```

4. **强制重新注入**
   ```
   1. 打开 debug.html 页面
   2. 点击"强制初始化"按钮
   3. 查看控制台输出
   ```

## 🔧 调试步骤

### 步骤 1: 基础检查

1. 打开 `debug.html` 或 `test-simple.html` 页面
2. 按 F12 打开开发者工具
3. 点击扩展图标
4. 查看控制台输出和页面状态

### 步骤 2: 手动测试

在控制台中运行以下命令：

```javascript
// 检查扩展状态
console.log('Sidebar:', document.getElementById('xpath-sidebar'));
console.log('Initialized:', window.xpathSidebarInitialized);

// 手动切换侧边栏
if (typeof toggleSidebar === 'function') {
  toggleSidebar();
} else {
  console.log('toggleSidebar function not found');
}

// 检查事件监听器
console.log('Event listeners attached');
```

### 步骤 3: 重新安装

如果以上方法都不行：

1. 完全卸载扩展
2. 重启浏览器
3. 重新安装扩展
4. 测试功能

## 📋 系统要求

- **Chrome**: 88+ (推荐 100+)
- **Edge**: 88+ (基于 Chromium)
- **操作系统**: Windows 10+, macOS 10.14+, Linux

## 🐛 报告问题

如果问题仍然存在，请提供以下信息：

1. **浏览器版本**
   ```
   在地址栏输入: chrome://version/
   ```

2. **扩展版本**
   ```
   在扩展管理页面查看版本号
   ```

3. **控制台错误信息**
   ```
   复制完整的错误堆栈
   ```

4. **重现步骤**
   ```
   详细描述操作步骤
   ```

## 💡 性能优化

### 减少内存使用

1. 不使用时关闭侧边栏
2. 避免在大型页面上频繁使用
3. 定期重启浏览器

### 提高响应速度

1. 确保页面完全加载后再使用
2. 避免在复杂的单页应用中使用
3. 使用简单的 XPath 表达式

---

**如果问题仍未解决，请查看项目的 GitHub Issues 或创建新的问题报告。**
