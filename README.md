# XPath 测试器 Chrome 扩展

一个美观、现代的 Chrome 扩展，专为爬虫工程师和网页开发者设计，用于快速生成和测试 XPath 表达式。

## ✨ 特性

- 🎨 **苹果风格设计** - 采用苹果公司的设计语言，美观大方
- 🌓 **深色/浅色模式** - 自动适配系统主题
- 📱 **侧边栏模式** - 与页面平分浏览器窗口，不影响页面操作
- ⚡ **实时预览** - 输入 XPath 表达式时实时显示匹配结果
- 🎯 **可视化选择** - 按住 Shift 键悬停元素自动生成 XPath
- 📋 **一键复制** - 快速复制 XPath 表达式和匹配结果
- 🔍 **智能高亮** - 美观的元素高亮效果
- 🚫 **非侵入式** - 点击页面其他地方不会关闭工具

## 🚀 安装方法

### 开发者模式安装

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择本项目文件夹
6. 扩展安装完成！

### 验证安装

1. **检查扩展图标** - 工具栏应显示 XPath 测试器图标
2. **测试功能** - 打开 `test.html` 或 `debug.html` 进行测试
3. **故障排除** - 如遇问题请查看 `TROUBLESHOOTING.md` 文件

## 📖 使用方法

### 基本使用

1. **打开侧边栏**
   - 点击浏览器工具栏中的 XPath 测试器图标
   - 侧边栏将从右侧滑出，占据浏览器窗口的一半

2. **可视化选择元素**
   - 在侧边栏打开状态下，按住 `Shift` 键
   - 将鼠标悬停在页面元素上（会显示蓝色高亮）
   - 点击元素自动生成 XPath 并填入输入框

3. **手动编辑 XPath**
   - 在侧边栏的输入框中直接输入或编辑 XPath 表达式
   - 实时查看匹配结果和数量

4. **复制结果**
   - 点击 "Copy" 按钮复制 XPath 表达式
   - 点击 "Copy All" 复制所有匹配结果
   - 点击单个结果项复制该项内容

5. **关闭侧边栏**
   - 点击侧边栏右上角的 "×" 按钮
   - 或再次点击浏览器工具栏中的扩展图标

### 高级功能

- **侧边栏模式**: 与页面内容平分屏幕，不会被页面操作影响
- **实时验证**: 输入 XPath 时自动验证语法并显示匹配数量
- **结果预览**: 显示匹配元素的文本内容和标签信息
- **智能提示**: 鼠标悬停查看元素详细信息
- **非侵入式**: 点击页面其他地方不会关闭侧边栏

## 🎯 常用 XPath 示例

```xpath
// 基本选择器
//div                          // 选择所有 div 元素
//div[@class='example']        // 选择特定 class 的 div
//button[@id='submit']         // 选择特定 id 的按钮

// 文本匹配
//a[text()='首页']             // 选择文本为"首页"的链接
//span[contains(text(),'搜索')] // 选择包含"搜索"文本的 span

// 层级关系
//div[@class='parent']//span   // 选择父级 div 下的所有 span
//ul/li[1]                     // 选择列表的第一项
//table//td[2]                 // 选择表格的第二列

// 属性匹配
//input[@type='text']          // 选择文本输入框
//img[contains(@src,'logo')]   // 选择包含 logo 的图片
//a[starts-with(@href,'http')] // 选择外部链接
```

## 🛠️ 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **扩展API**: Chrome Extension Manifest V3
- **设计系统**: Apple Human Interface Guidelines
- **字体**: SF Pro Display, SF Pro Text, SF Mono

## 📁 项目结构

```
xpath-picker/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本
├── content.js             # 内容脚本（侧边栏逻辑）
├── sidebar.css            # 侧边栏样式
├── popup.html             # 弹窗页面（备用）
├── popup.css              # 弹窗样式（备用）
├── popup.js               # 弹窗逻辑（备用）
├── test.html              # 测试页面
├── debug.html             # 调试页面
├── TROUBLESHOOTING.md     # 故障排除指南
├── INSTALL.md             # 安装指南
└── README.md              # 说明文档
```

## 🎨 设计特色

### 颜色系统
- **主色调**: #007AFF (苹果蓝)
- **辅助色**: #5AC8FA (浅蓝)
- **成功色**: #34C759 (绿色)
- **文字色**: 动态适配深浅模式

### 交互设计
- **微动画**: 按钮点击、悬停效果
- **反馈机制**: 复制成功提示
- **无障碍**: 完整的键盘导航支持

### 响应式布局
- **侧边栏尺寸**: 占据浏览器窗口50%宽度
- **圆角设计**: 12px 大圆角
- **阴影效果**: 多层次阴影
- **动画效果**: 平滑的滑入滑出动画

## 🔧 开发说明

### 本地开发

1. 克隆项目到本地
2. 在 Chrome 中加载扩展
3. 修改代码后刷新扩展
4. 使用 `test.html` 进行功能测试

### 代码规范

- 使用 ES6+ 语法
- 遵循苹果设计规范
- 注重用户体验和性能

## 📝 更新日志

### v1.1.0 (2024-07-28)
- 🆕 **侧边栏模式** - 与页面平分浏览器窗口
- 🚫 **非侵入式设计** - 点击页面不会关闭工具
- ✨ 平滑的滑入滑出动画效果
- 🎨 优化的苹果风格界面设计

### v1.0.0 (2024-07-28)
- ✨ 初始版本发布
- 🎨 苹果风格界面设计
- ⚡ 实时 XPath 测试功能
- 🎯 可视化元素选择
- 📋 一键复制功能

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🙏 致谢

- 感谢 Apple 公司的设计灵感
- 感谢 Chrome 扩展开发社区
- 感谢所有贡献者和用户

---

**享受使用 XPath 测试器！** 🚀
