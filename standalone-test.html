<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath测试器 - 独立测试</title>
    <link rel="stylesheet" href="sidebar.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-element {
            padding: 15px;
            margin: 10px 0;
            background: #f0f0f0;
            border-radius: 8px;
            border: 2px solid #ddd;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .test-element:hover {
            border-color: #007AFF;
            background: #f8f9ff;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }
        .btn:hover {
            background: #0056CC;
        }
        .success {
            background: #34C759;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            background: #FF3B30;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .warning {
            background: #FF9500;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 XPath测试器 - 独立测试</h1>
        <p>这个页面直接加载content script，不依赖扩展注入</p>
    </div>

    <div class="status-card">
        <h2>🎮 控制面板</h2>
        <div class="controls">
            <button class="btn" onclick="loadScript()">加载脚本</button>
            <button class="btn" onclick="checkStatus()">检查状态</button>
            <button class="btn" onclick="testToggle()">测试切换</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="status-display">等待操作...</div>
    </div>

    <div class="status-card">
        <h2>📋 日志输出</h2>
        <div id="log-output" class="log">等待日志...</div>
    </div>

    <div class="status-card">
        <h2>🧪 测试元素</h2>
        <p>加载脚本后，按住 <kbd>Shift</kbd> 键并点击下面的元素：</p>
        
        <div class="test-element" id="element-1" data-test="first">
            <h3>测试元素 1</h3>
            <p>这是第一个测试元素，ID为 "element-1"</p>
        </div>
        
        <div class="test-element" class="special-class" data-test="second">
            <h3>测试元素 2</h3>
            <p>这是第二个测试元素，带有特殊class</p>
            <button onclick="alert('按钮被点击')">测试按钮</button>
        </div>
        
        <div class="test-element" data-test="third">
            <h3>测试元素 3</h3>
            <ul>
                <li>列表项 1</li>
                <li>列表项 2</li>
                <li>列表项 3</li>
            </ul>
        </div>
    </div>

    <script>
        let statusDisplay = document.getElementById('status-display');
        let logOutput = document.getElementById('log-output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            
            logOutput.textContent += logMessage + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(logMessage);
        }
        
        function updateStatus(message, type = 'info') {
            const className = type === 'success' ? 'success' : 
                             type === 'error' ? 'error' : 
                             type === 'warning' ? 'warning' : '';
            
            statusDisplay.innerHTML = `<div class="${className}">${message}</div>`;
            log(message, type);
        }
        
        function loadScript() {
            updateStatus('正在加载content script...', 'info');
            
            // 先加载CSS
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'sidebar.css';
            document.head.appendChild(link);
            log('✅ CSS已加载');
            
            // 再加载JS
            const script = document.createElement('script');
            script.src = 'content-test.js';
            
            script.onload = () => {
                log('✅ JavaScript脚本加载完成');
                updateStatus('脚本加载成功！', 'success');
                
                // 等待一下再检查状态
                setTimeout(checkStatus, 500);
            };
            
            script.onerror = () => {
                log('❌ JavaScript脚本加载失败');
                updateStatus('脚本加载失败！', 'error');
            };
            
            document.head.appendChild(script);
        }
        
        function checkStatus() {
            log('🔍 开始检查状态...');
            
            const checks = {
                initialized: !!window.xpathSidebarInitialized,
                sidebar: !!document.getElementById('xpath-sidebar'),
                toggleSidebar: typeof window.toggleSidebar === 'function',
                showSidebar: typeof window.showSidebar === 'function',
                hideSidebar: typeof window.hideSidebar === 'function',
                createSidebar: typeof window.createSidebar === 'function',
                instance: !!window.xpathSidebarInstance
            };
            
            log('📊 检查结果:');
            Object.entries(checks).forEach(([key, value]) => {
                log(`  ${key}: ${value ? '✅' : '❌'}`);
            });
            
            const passed = Object.values(checks).filter(v => v).length;
            const total = Object.keys(checks).length;
            
            if (passed === total) {
                updateStatus(`🎉 所有检查通过！(${passed}/${total})`, 'success');
            } else if (passed > 0) {
                updateStatus(`⚠️ 部分功能可用 (${passed}/${total})`, 'warning');
            } else {
                updateStatus(`❌ 功能不可用 (${passed}/${total})`, 'error');
            }
            
            // 列出所有xpath相关的全局变量
            const xpathVars = Object.keys(window).filter(key => 
                key.toLowerCase().includes('xpath') || 
                key.toLowerCase().includes('sidebar') ||
                key.toLowerCase().includes('toggle')
            );
            
            if (xpathVars.length > 0) {
                log('🔍 找到的相关变量: ' + xpathVars.join(', '));
            } else {
                log('❌ 没有找到相关变量');
            }
        }
        
        function testToggle() {
            log('🧪 测试切换功能...');
            
            if (typeof window.toggleSidebar === 'function') {
                try {
                    window.toggleSidebar();
                    log('✅ toggleSidebar() 调用成功');
                    updateStatus('切换功能正常！', 'success');
                } catch (error) {
                    log(`❌ toggleSidebar() 调用失败: ${error.message}`);
                    updateStatus('切换功能失败！', 'error');
                }
            } else {
                log('❌ toggleSidebar 函数不存在');
                updateStatus('切换函数不存在！', 'error');
            }
        }
        
        function clearLog() {
            logOutput.textContent = '日志已清空...\n';
            updateStatus('日志已清空', 'info');
        }
        
        // 监听键盘事件
        let shiftPressed = false;
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && !shiftPressed) {
                shiftPressed = true;
                log('⌨️ Shift键已按下');
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                shiftPressed = false;
                log('⌨️ Shift键已释放');
            }
        });
        
        // 监听点击事件
        document.addEventListener('click', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                const element = e.target.closest('.test-element');
                log(`🖱️ Shift+点击了: ${element.querySelector('h3').textContent}`);
            }
        });
        
        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            log('🎉 检测到侧边栏元素被创建！');
                            updateStatus('侧边栏已创建！', 'success');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            log('📄 页面加载完成');
            updateStatus('页面已准备就绪，点击"加载脚本"开始测试', 'info');
        });
    </script>
</body>
</html>
