// XPath Sidebar Content Script - 测试版本
console.log('🚀 XPath Sidebar (Test) loaded at:', new Date().toLocaleTimeString());

// 立即设置全局变量
window.xpathSidebarInitialized = true;
window.sidebarVisible = false;
window.sidebar = null;

// 立即设置全局函数
window.toggleSidebar = function() {
    console.log('🔄 toggleSidebar called');
    if (window.sidebarVisible) {
        window.hideSidebar();
    } else {
        window.showSidebar();
    }
};

window.showSidebar = function() {
    console.log('📖 showSidebar called');
    if (!window.sidebar) {
        window.createSidebar();
    }

    window.sidebarVisible = true;
    window.sidebar.classList.add('xpath-sidebar-visible');
    document.body.classList.add('xpath-sidebar-open');
    console.log('✅ Sidebar shown');
};

window.hideSidebar = function() {
    console.log('📕 hideSidebar called');
    window.sidebarVisible = false;
    if (window.sidebar) {
        window.sidebar.classList.remove('xpath-sidebar-visible');
    }
    document.body.classList.remove('xpath-sidebar-open');
    console.log('✅ Sidebar hidden');
};

window.createSidebar = function() {
    if (window.sidebar) {
        console.log('Sidebar already exists');
        return;
    }

    console.log('🏗️ Creating sidebar...');
    window.sidebar = document.createElement('div');
    window.sidebar.id = 'xpath-sidebar';

    // 使用简单的字符串拼接而不是模板字符串
    window.sidebar.innerHTML =
        '<div class="xpath-sidebar-header">' +
            '<span class="xpath-sidebar-title">XPath 测试器</span>' +
            '<button class="xpath-close-btn" onclick="window.hideSidebar()">×</button>' +
        '</div>' +
        '<div class="xpath-sidebar-tip">' +
            'Hold shift and hover the element – Or edit XPath text' +
        '</div>' +
        '<div class="xpath-sidebar-input-section">' +
            '<div class="xpath-sidebar-label">' +
                'XPath' +
                '<button class="xpath-copy-btn" onclick="window.copyXPath()">Copy</button>' +
            '</div>' +
            '<textarea class="xpath-input" id="xpath-input" placeholder="输入XPath表达式..."></textarea>' +
        '</div>' +
        '<div class="xpath-sidebar-results-section">' +
            '<div class="xpath-sidebar-results-header">' +
                '<span>Results</span>' +
                '<span class="xpath-results-count" id="xpath-results-count">0 items</span>' +
                '<button class="xpath-copy-all-btn" onclick="window.copyAllResults()">Copy All</button>' +
            '</div>' +
            '<div class="xpath-results-container" id="xpath-results-container">' +
                '<!-- 结果将在这里显示 -->' +
            '</div>' +
        '</div>';

    document.body.appendChild(window.sidebar);
    console.log('✅ Sidebar created and added to DOM');
};

window.copyXPath = function() {
    const input = document.getElementById('xpath-input');
    if (input && input.value) {
        navigator.clipboard.writeText(input.value).then(() => {
            console.log('✅ XPath copied');
        }).catch(err => {
            console.error('❌ Copy failed:', err);
        });
    }
};

window.copyAllResults = function() {
    console.log('📋 Copy all results called');
};

// 创建实例对象
window.xpathSidebarInstance = {
    sidebar: window.sidebar,
    sidebarVisible: window.sidebarVisible,
    toggleSidebar: window.toggleSidebar,
    showSidebar: window.showSidebar,
    hideSidebar: window.hideSidebar,
    createSidebar: window.createSidebar
};

// 设置事件监听器
window.setupEventListeners = function() {
    console.log('🎧 Setting up event listeners...');

    // Shift键监听
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Shift') {
            if (window.sidebarVisible) {
                document.body.style.cursor = 'crosshair';
                console.log('🎯 Crosshair cursor enabled (sidebar visible)');
            } else {
                console.log('🎯 Shift pressed but sidebar not visible');
            }
        }
    });

    document.addEventListener('keyup', (e) => {
        if (e.key === 'Shift') {
            document.body.style.cursor = 'default';
            window.removeHighlight();
            console.log('🎯 Crosshair cursor disabled');
        }
    });

    // 鼠标事件监听
    document.addEventListener('mouseover', (e) => {
        if (e.shiftKey) {
            console.log('🖱️ Mouseover with Shift:', {
                sidebarVisible: window.sidebarVisible,
                hasSidebar: !!window.sidebar,
                targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false,
                target: e.target.tagName
            });

            if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                window.highlightElement(e.target);
            }
        }
    });

    document.addEventListener('click', (e) => {
        if (e.shiftKey) {
            console.log('🖱️ Click with Shift:', {
                sidebarVisible: window.sidebarVisible,
                hasSidebar: !!window.sidebar,
                targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false,
                target: e.target.tagName
            });

            if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                const xpath = window.getXPath(e.target);
                window.updateXPathInput(xpath);
                console.log('🎯 Element clicked, XPath generated:', xpath);
            }
        }
    });

    console.log('✅ Event listeners set up');
};

// 高亮元素功能
window.highlightElement = function(element) {
    window.removeHighlight();

    const rect = element.getBoundingClientRect();
    const overlay = document.createElement('div');
    overlay.className = 'xpath-highlight-overlay';
    overlay.style.cssText =
        'position: fixed !important;' +
        'top: ' + rect.top + 'px !important;' +
        'left: ' + rect.left + 'px !important;' +
        'width: ' + rect.width + 'px !important;' +
        'height: ' + rect.height + 'px !important;' +
        'background: rgba(0, 122, 255, 0.15) !important;' +
        'border: 2px solid #007AFF !important;' +
        'border-radius: 8px !important;' +
        'pointer-events: none !important;' +
        'z-index: 2147483646 !important;' +
        'box-sizing: border-box !important;' +
        'box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3) !important;';

    document.body.appendChild(overlay);
    window.currentOverlay = overlay;
    console.log('🎯 Element highlighted:', element.tagName);
};

window.removeHighlight = function() {
    if (window.currentOverlay) {
        window.currentOverlay.remove();
        window.currentOverlay = null;
    }
};

// XPath生成功能
window.getXPath = function(element) {
    if (element.id) {
        return '//*[@id="' + element.id + '"]';
    }

    let path = '';
    while (element && element.nodeType === Node.ELEMENT_NODE) {
        let selector = element.nodeName.toLowerCase();
        if (element.className) {
            selector += '[@class="' + element.className + '"]';
        }
        path = '/' + selector + path;
        element = element.parentNode;
    }
    return path;
};

// 更新XPath输入框
window.updateXPathInput = function(xpath) {
    const input = document.getElementById('xpath-input');
    if (input) {
        input.value = xpath;
        console.log('📝 XPath updated in input:', xpath);
        // 这里可以添加执行XPath的逻辑
    }
};

// 消息监听器
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('📨 Received message:', request);
        if (request.action === 'toggleSidebar') {
            window.toggleSidebar();
            sendResponse({ success: true });
        }
        return true;
    });
}

// 立即设置事件监听器
window.setupEventListeners();

console.log('✅ XPath Sidebar (Test) setup completed');
console.log('📊 Global functions available:', {
    toggleSidebar: typeof window.toggleSidebar,
    showSidebar: typeof window.showSidebar,
    hideSidebar: typeof window.hideSidebar,
    createSidebar: typeof window.createSidebar,
    initialized: window.xpathSidebarInitialized,
    instance: !!window.xpathSidebarInstance
});
