// XPath Sidebar Content Script - 测试版本
console.log('🚀 XPath Sidebar (Test) loaded at:', new Date().toLocaleTimeString());

// 立即设置全局变量
window.xpathSidebarInitialized = true;
window.sidebarVisible = false;
window.sidebar = null;

// 立即设置全局函数
window.toggleSidebar = function() {
    console.log('🔄 toggleSidebar called');
    if (window.sidebarVisible) {
        window.hideSidebar();
    } else {
        window.showSidebar();
    }
};

window.showSidebar = function() {
    console.log('📖 showSidebar called');
    if (!window.sidebar) {
        window.createSidebar();
    }

    window.sidebarVisible = true;
    window.sidebar.classList.add('xpath-sidebar-visible');
    document.body.classList.add('xpath-sidebar-open');
    console.log('✅ Sidebar shown');
};

window.hideSidebar = function() {
    console.log('📕 hideSidebar called');
    window.sidebarVisible = false;
    if (window.sidebar) {
        window.sidebar.classList.remove('xpath-sidebar-visible');
    }
    document.body.classList.remove('xpath-sidebar-open');

    // 清理状态
    document.body.style.cursor = 'default';
    window.removeHighlight();

    console.log('✅ Sidebar hidden and state cleaned');
};

window.createSidebar = function() {
    if (window.sidebar) {
        console.log('Sidebar already exists');
        return;
    }

    console.log('🏗️ Creating sidebar...');
    window.sidebar = document.createElement('div');
    window.sidebar.id = 'xpath-sidebar';

    // 使用简单的字符串拼接而不是模板字符串
    window.sidebar.innerHTML =
        '<div class="xpath-sidebar-header">' +
            '<span class="xpath-sidebar-title">XPath 测试器</span>' +
            '<button class="xpath-close-btn" id="xpath-close-btn">×</button>' +
        '</div>' +
        '<div class="xpath-sidebar-tip">' +
            'Hold shift and hover the element – Or edit XPath text' +
        '</div>' +
        '<div class="xpath-sidebar-input-section">' +
            '<div class="xpath-sidebar-label">' +
                'XPath' +
                '<div class="xpath-button-group">' +
                    '<button class="xpath-clear-btn" id="xpath-clear-btn">Clear</button>' +
                    '<button class="xpath-copy-btn" id="xpath-copy-btn">Copy</button>' +
                '</div>' +
            '</div>' +
            '<textarea class="xpath-input" id="xpath-input" placeholder="输入XPath表达式..."></textarea>' +
        '</div>' +
        '<div class="xpath-sidebar-results-section">' +
            '<div class="xpath-sidebar-results-header">' +
                '<span>Results</span>' +
                '<span class="xpath-results-count" id="xpath-results-count">0 items</span>' +
                '<button class="xpath-copy-all-btn" id="xpath-copy-all-btn">Copy All</button>' +
            '</div>' +
            '<div class="xpath-results-container" id="xpath-results-container">' +
                '<!-- 结果将在这里显示 -->' +
            '</div>' +
        '</div>';

    document.body.appendChild(window.sidebar);

    // 使用setTimeout确保DOM元素完全渲染后再添加事件监听器
    setTimeout(() => {
        window.setupSidebarEventListeners();
    }, 0);

    console.log('✅ Sidebar created and added to DOM');
};

// 设置侧边栏事件监听器
window.setupSidebarEventListeners = function() {
    console.log('🔧 Setting up sidebar event listeners...');

    const closeBtn = document.getElementById('xpath-close-btn');
    const copyBtn = document.getElementById('xpath-copy-btn');
    const clearBtn = document.getElementById('xpath-clear-btn');
    const copyAllBtn = document.getElementById('xpath-copy-all-btn');
    const xpathInput = document.getElementById('xpath-input');

    console.log('🔍 Elements found:', {
        closeBtn: !!closeBtn,
        copyBtn: !!copyBtn,
        clearBtn: !!clearBtn,
        copyAllBtn: !!copyAllBtn,
        xpathInput: !!xpathInput
    });

    // 关闭按钮
    if (closeBtn) {
        closeBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔴 Close button clicked');
            window.hideSidebar();
        });
        console.log('✅ Close button listener added');
    } else {
        console.error('❌ Close button not found!');
    }

    // 复制按钮
    if (copyBtn) {
        copyBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            window.copyXPath();
        });
        console.log('✅ Copy button listener added');
    } else {
        console.error('❌ Copy button not found!');
    }

    // 清除按钮
    if (clearBtn) {
        clearBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            window.clearXPath();
        });
        console.log('✅ Clear button listener added');
    } else {
        console.error('❌ Clear button not found!');
    }

    // 复制所有结果按钮
    if (copyAllBtn) {
        copyAllBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            window.copyAllResults();
        });
        console.log('✅ Copy all button listener added');
    } else {
        console.error('❌ Copy all button not found!');
    }

    // 输入框事件监听
    if (xpathInput) {
        xpathInput.addEventListener('input', () => {
            window.executeXPath();
        });
        console.log('✅ Input listener added');
    } else {
        console.error('❌ XPath input not found!');
    }

    console.log('✅ Sidebar event listeners setup completed');
};

window.copyXPath = function() {
    const input = document.getElementById('xpath-input');
    if (input && input.value) {
        navigator.clipboard.writeText(input.value).then(() => {
            console.log('✅ XPath copied');
        }).catch(err => {
            console.error('❌ Copy failed:', err);
        });
    }
};

window.clearXPath = function() {
    const input = document.getElementById('xpath-input');
    if (input) {
        input.value = '';
        console.log('🧹 XPath cleared');
        // 清除高亮
        window.removeHighlight();
        // 清除结果
        window.updateResults([]);
    }
};

window.copyAllResults = function() {
    console.log('📋 Copy all results called');
    // 这里可以添加复制所有结果的逻辑
};

// 创建实例对象
window.xpathSidebarInstance = {
    sidebar: window.sidebar,
    sidebarVisible: window.sidebarVisible,
    toggleSidebar: window.toggleSidebar,
    showSidebar: window.showSidebar,
    hideSidebar: window.hideSidebar,
    createSidebar: window.createSidebar
};

// 设置事件监听器
window.setupEventListeners = function() {
    console.log('🎧 Setting up event listeners...');

    // Shift键监听
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Shift') {
            if (window.sidebarVisible) {
                document.body.style.cursor = 'crosshair';
                console.log('🎯 Crosshair cursor enabled (sidebar visible)');
            } else {
                console.log('🎯 Shift pressed but sidebar not visible');
            }
        }
    });

    document.addEventListener('keyup', (e) => {
        if (e.key === 'Shift') {
            document.body.style.cursor = 'default';
            window.removeHighlight();
            console.log('🎯 Crosshair cursor disabled');
        }
    });

    // 鼠标事件监听
    document.addEventListener('mouseover', (e) => {
        if (e.shiftKey) {
            console.log('🖱️ Mouseover with Shift:', {
                sidebarVisible: window.sidebarVisible,
                hasSidebar: !!window.sidebar,
                targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false,
                target: e.target.tagName
            });

            if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                window.highlightElement(e.target);
            }
        }
    });

    document.addEventListener('click', (e) => {
        if (e.shiftKey) {
            console.log('🖱️ Click with Shift:', {
                sidebarVisible: window.sidebarVisible,
                hasSidebar: !!window.sidebar,
                targetInSidebar: window.sidebar ? window.sidebar.contains(e.target) : false,
                target: e.target.tagName
            });

            if (window.sidebarVisible && window.sidebar && !window.sidebar.contains(e.target)) {
                e.preventDefault();
                e.stopPropagation();
                const xpath = window.getXPath(e.target);
                window.updateXPathInput(xpath);
                console.log('🎯 Element clicked, XPath generated:', xpath);
            }
        }
    });

    console.log('✅ Event listeners set up');
};

// 高亮元素功能
window.highlightElement = function(element) {
    window.removeHighlight();

    const rect = element.getBoundingClientRect();
    const overlay = document.createElement('div');
    overlay.className = 'xpath-highlight-overlay';
    overlay.style.cssText =
        'position: fixed !important;' +
        'top: ' + rect.top + 'px !important;' +
        'left: ' + rect.left + 'px !important;' +
        'width: ' + rect.width + 'px !important;' +
        'height: ' + rect.height + 'px !important;';

    document.body.appendChild(overlay);
    window.currentOverlay = overlay;
    console.log('🎯 Element highlighted:', element.tagName);
};

window.removeHighlight = function() {
    if (window.currentOverlay) {
        window.currentOverlay.remove();
        window.currentOverlay = null;
    }
};

// XPath生成功能
window.getXPath = function(element) {
    if (element.id) {
        return '//*[@id="' + element.id + '"]';
    }

    let path = '';
    while (element && element.nodeType === Node.ELEMENT_NODE) {
        let selector = element.nodeName.toLowerCase();
        if (element.className) {
            selector += '[@class="' + element.className + '"]';
        }
        path = '/' + selector + path;
        element = element.parentNode;
    }
    return path;
};

// 更新XPath输入框
window.updateXPathInput = function(xpath) {
    const input = document.getElementById('xpath-input');
    if (input) {
        input.value = xpath;
        console.log('📝 XPath updated in input:', xpath);
        window.executeXPath();
    }
};

// 执行XPath查询
window.executeXPath = function() {
    const input = document.getElementById('xpath-input');
    const xpath = input ? input.value.trim() : '';

    if (!xpath) {
        window.updateResults([]);
        return;
    }

    try {
        const result = document.evaluate(xpath, document, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);
        const elements = [];

        for (let i = 0; i < result.snapshotLength; i++) {
            const element = result.snapshotItem(i);
            elements.push({
                text: element.textContent.trim().substring(0, 100) || '[无文本]',
                tagName: element.tagName,
                element: element
            });
        }

        window.updateResults(elements);
        console.log('✅ XPath executed successfully, found', elements.length, 'elements');
    } catch (error) {
        console.error('❌ XPath execution error:', error);
        window.updateResults([]);
    }
};

// 更新结果显示
window.updateResults = function(elements) {
    const container = document.getElementById('xpath-results-container');
    const countElement = document.getElementById('xpath-results-count');

    if (!container || !countElement) {
        console.error('❌ Results container or count element not found');
        return;
    }

    // 更新计数
    countElement.textContent = elements.length + ' items';

    // 清空容器
    container.innerHTML = '';

    // 添加结果
    elements.forEach((item, index) => {
        const resultItem = document.createElement('div');
        resultItem.className = 'xpath-result-item';
        resultItem.innerHTML =
            '<div class="xpath-result-tag">' + (item.tagName || 'TEXT') + '</div>' +
            '<div class="xpath-result-text">' + item.text + '</div>';

        // 添加点击事件来高亮对应元素
        if (item.element) {
            resultItem.addEventListener('click', () => {
                window.highlightElement(item.element);
                item.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });
        }

        container.appendChild(resultItem);
    });

    console.log('📊 Results updated:', elements.length, 'items');
};

// 消息监听器
if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        console.log('📨 Received message:', request);
        if (request.action === 'toggleSidebar') {
            window.toggleSidebar();
            sendResponse({ success: true });
        }
        return true;
    });
}

// 立即设置事件监听器
window.setupEventListeners();

console.log('✅ XPath Sidebar (Test) setup completed');
console.log('📊 Global functions available:', {
    toggleSidebar: typeof window.toggleSidebar,
    showSidebar: typeof window.showSidebar,
    hideSidebar: typeof window.hideSidebar,
    createSidebar: typeof window.createSidebar,
    initialized: window.xpathSidebarInitialized,
    instance: !!window.xpathSidebarInstance
});
