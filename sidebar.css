/* XPath测试器侧边栏样式 */

/* Apple-style design system */
:root {
  --xpath-primary-blue: #007AFF;
  --xpath-primary-blue-hover: #0056CC;
  --xpath-secondary-blue: #5AC8FA;
  --xpath-green: #34C759;
  --xpath-green-hover: #30B050;
  --xpath-orange: #FF9500;
  --xpath-red: #FF3B30;
  --xpath-gray-1: #8E8E93;
  --xpath-gray-2: #AEAEB2;
  --xpath-gray-3: #C7C7CC;
  --xpath-gray-4: #D1D1D6;
  --xpath-gray-5: #E5E5EA;
  --xpath-gray-6: #F2F2F7;
  --xpath-background-primary: #FFFFFF;
  --xpath-background-secondary: #F2F2F7;
  --xpath-background-tertiary: #FFFFFF;
  --xpath-text-primary: #000000;
  --xpath-text-secondary: #3C3C43;
  --xpath-text-tertiary: #3C3C4399;
  --xpath-separator: #3C3C4329;
  --xpath-fill-quaternary: #7676801F;
  --xpath-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  --xpath-shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --xpath-border-radius: 12px;
  --xpath-border-radius-small: 8px;
  --xpath-border-radius-large: 16px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --xpath-background-primary: #000000;
    --xpath-background-secondary: #1C1C1E;
    --xpath-background-tertiary: #2C2C2E;
    --xpath-text-primary: #FFFFFF;
    --xpath-text-secondary: #EBEBF5;
    --xpath-text-tertiary: #EBEBF599;
    --xpath-separator: #54545899;
    --xpath-fill-quaternary: #7676803D;
    --xpath-gray-6: #1C1C1E;
  }
}

/* 侧边栏容器 */
#xpath-sidebar {
  position: fixed !important;
  top: 0 !important;
  right: -50vw !important;
  width: 50vw !important;
  height: 100vh !important;
  background: var(--xpath-background-primary) !important;
  border-left: 1px solid var(--xpath-separator) !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
  z-index: 2147483647 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--xpath-text-primary) !important;
  transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* 侧边栏显示状态 */
#xpath-sidebar.xpath-sidebar-visible {
  right: 0 !important;
}

/* 页面内容调整 */
body.xpath-sidebar-open {
  margin-right: 50vw !important;
  transition: margin-right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* 侧边栏头部 */
.xpath-sidebar-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 16px 20px !important;
  background: var(--xpath-background-primary) !important;
  border-bottom: 0.5px solid var(--xpath-separator) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

.xpath-sidebar-title {
  font-size: 17px !important;
  font-weight: 600 !important;
  color: var(--xpath-text-primary) !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.xpath-sidebar-title::before {
  content: "</>" !important;
  color: var(--xpath-primary-blue) !important;
  font-weight: 700 !important;
  font-size: 16px !important;
}

.xpath-close-btn {
  background: var(--xpath-fill-quaternary) !important;
  border: none !important;
  color: var(--xpath-text-secondary) !important;
  font-size: 16px !important;
  cursor: pointer !important;
  padding: 6px !important;
  width: 28px !important;
  height: 28px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  font-weight: 500 !important;
}

.xpath-close-btn:hover {
  background: var(--xpath-gray-5) !important;
  color: var(--xpath-text-primary) !important;
  transform: scale(1.05) !important;
}

.xpath-close-btn:active {
  transform: scale(0.95) !important;
}

/* 提示区域 */
.xpath-sidebar-tip {
  padding: 16px 20px !important;
  background: linear-gradient(135deg, var(--xpath-secondary-blue), var(--xpath-primary-blue)) !important;
  color: var(--xpath-background-primary) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  border-bottom: 0.5px solid var(--xpath-separator) !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

/* XPath输入区域 */
.xpath-sidebar-input-section {
  padding: 20px !important;
  background: var(--xpath-background-primary) !important;
  border-bottom: 0.5px solid var(--xpath-separator) !important;
}

.xpath-sidebar-label {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  font-size: 15px !important;
  font-weight: 600 !important;
  margin-bottom: 12px !important;
  color: var(--xpath-text-primary) !important;
}

.xpath-copy-btn {
  background: var(--xpath-green) !important;
  color: var(--xpath-background-primary) !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: var(--xpath-border-radius-small) !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  box-shadow: var(--xpath-shadow-light) !important;
}

.xpath-copy-btn:hover {
  background: var(--xpath-green-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--xpath-shadow) !important;
}

.xpath-copy-btn:active {
  transform: translateY(0) !important;
}

.xpath-input {
  width: 100% !important;
  height: 90px !important;
  background: var(--xpath-background-secondary) !important;
  border: 1px solid var(--xpath-separator) !important;
  border-radius: var(--xpath-border-radius) !important;
  color: var(--xpath-text-primary) !important;
  padding: 12px 16px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  resize: vertical !important;
  transition: all 0.2s ease !important;
  box-sizing: border-box !important;
}

.xpath-input:focus {
  outline: none !important;
  border-color: var(--xpath-primary-blue) !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.25) !important;
}

.xpath-input::placeholder {
  color: var(--xpath-text-tertiary) !important;
}

/* 结果区域 */
.xpath-sidebar-results-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  padding: 20px !important;
  background: var(--xpath-background-primary) !important;
  overflow: hidden !important;
}

.xpath-sidebar-results-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 16px !important;
}

.xpath-sidebar-results-header span:first-child {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: var(--xpath-text-primary) !important;
}

.xpath-results-count {
  color: var(--xpath-text-tertiary) !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  background: var(--xpath-background-secondary) !important;
  padding: 4px 8px !important;
  border-radius: var(--xpath-border-radius-small) !important;
}

.xpath-copy-all-btn {
  background: var(--xpath-background-secondary) !important;
  color: var(--xpath-primary-blue) !important;
  border: 1px solid var(--xpath-separator) !important;
  padding: 6px 12px !important;
  border-radius: var(--xpath-border-radius-small) !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.xpath-copy-all-btn:hover {
  background: var(--xpath-primary-blue) !important;
  color: var(--xpath-background-primary) !important;
  border-color: var(--xpath-primary-blue) !important;
}

.xpath-copy-all-btn:active {
  transform: scale(0.98) !important;
}

.xpath-results-container {
  flex: 1 !important;
  background: var(--xpath-background-secondary) !important;
  border: 1px solid var(--xpath-separator) !important;
  border-radius: var(--xpath-border-radius) !important;
  padding: 0 !important;
  overflow-y: auto !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 12px !important;
  box-shadow: var(--xpath-shadow-light) !important;
  counter-reset: xpath-result-counter !important;
}

.xpath-results-container:empty::before {
  content: "暂无结果" !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  color: var(--xpath-text-tertiary) !important;
  font-style: italic !important;
}

.xpath-result-item {
  padding: 12px 16px 12px 44px !important;
  border-bottom: 0.5px solid var(--xpath-separator) !important;
  cursor: pointer !important;
  transition: all 0.15s ease !important;
  color: var(--xpath-text-secondary) !important;
  line-height: 1.4 !important;
  position: relative !important;
  animation: xpath-fadeIn 0.3s ease !important;
}

.xpath-result-item:hover {
  background: rgba(0, 122, 255, 0.1) !important;
  color: var(--xpath-text-primary) !important;
}

.xpath-result-item:active {
  background: rgba(0, 122, 255, 0.2) !important;
}

.xpath-result-item:last-child {
  border-bottom: none !important;
}

.xpath-result-item::before {
  content: counter(xpath-result-counter) !important;
  counter-increment: xpath-result-counter !important;
  position: absolute !important;
  left: 16px !important;
  top: 12px !important;
  background: var(--xpath-primary-blue) !important;
  color: var(--xpath-background-primary) !important;
  font-size: 10px !important;
  font-weight: 600 !important;
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 滚动条样式 */
.xpath-results-container::-webkit-scrollbar {
  width: 6px !important;
}

.xpath-results-container::-webkit-scrollbar-track {
  background: transparent !important;
}

.xpath-results-container::-webkit-scrollbar-thumb {
  background: var(--xpath-gray-3) !important;
  border-radius: 3px !important;
}

.xpath-results-container::-webkit-scrollbar-thumb:hover {
  background: var(--xpath-gray-2) !important;
}

/* 动画 */
@keyframes xpath-fadeIn {
  from {
    opacity: 0 !important;
    transform: translateY(10px) !important;
  }
  to {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }
}

/* 焦点状态 */
.xpath-copy-btn:focus,
.xpath-copy-all-btn:focus,
.xpath-close-btn:focus {
  outline: 2px solid var(--xpath-primary-blue) !important;
  outline-offset: 2px !important;
}

.xpath-input:focus {
  outline: none !important;
  border-color: var(--xpath-primary-blue) !important;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.25) !important;
}

/* 高亮覆盖层样式 */
.xpath-highlight-overlay {
  position: fixed !important;
  background: rgba(0, 122, 255, 0.15) !important;
  border: 2px solid #007AFF !important;
  border-radius: 8px !important;
  pointer-events: none !important;
  z-index: 2147483646 !important;
  box-sizing: border-box !important;
  box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  transition: all 0.2s ease !important;
}
