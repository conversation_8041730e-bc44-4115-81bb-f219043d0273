/* Apple-style design system */
:root {
  --primary-blue: #007AFF;
  --primary-blue-hover: #0056CC;
  --secondary-blue: #5AC8FA;
  --green: #34C759;
  --green-hover: #30B050;
  --orange: #FF9500;
  --red: #FF3B30;
  --gray-1: #8E8E93;
  --gray-2: #AEAEB2;
  --gray-3: #C7C7CC;
  --gray-4: #D1D1D6;
  --gray-5: #E5E5EA;
  --gray-6: #F2F2F7;
  --background-primary: #FFFFFF;
  --background-secondary: #F2F2F7;
  --background-tertiary: #FFFFFF;
  --text-primary: #000000;
  --text-secondary: #3C3C43;
  --text-tertiary: #3C3C4399;
  --separator: #3C3C4329;
  --fill-quaternary: #7676801F;
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --border-radius: 12px;
  --border-radius-small: 8px;
  --border-radius-large: 16px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #000000;
    --background-secondary: #1C1C1E;
    --background-tertiary: #2C2C2E;
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #EBEBF599;
    --separator: #54545899;
    --fill-quaternary: #7676803D;
    --gray-6: #1C1C1E;
  }
}

body {
  margin: 0;
  padding: 0;
  width: 420px;
  height: 640px;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  background: var(--background-primary);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--background-primary);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--background-primary);
  border-bottom: 0.5px solid var(--separator);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.title {
  font-size: 17px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.title::before {
  content: "</>";
  color: var(--primary-blue);
  font-weight: 700;
  font-size: 16px;
}

.close-btn {
  background: var(--fill-quaternary);
  border: none;
  color: var(--text-secondary);
  font-size: 16px;
  cursor: pointer;
  padding: 6px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-weight: 500;
}

.close-btn:hover {
  background: var(--gray-5);
  color: var(--text-primary);
  transform: scale(1.05);
}

.close-btn:active {
  transform: scale(0.95);
}

.tip {
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
  color: var(--background-primary);
  font-size: 13px;
  font-weight: 500;
  border-bottom: 0.5px solid var(--separator);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
  opacity: 0.9;
  z-index: -1;
}

.xpath-section {
  padding: 20px;
  background: var(--background-primary);
  border-bottom: 0.5px solid var(--separator);
}

.xpath-section label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.copy-btn {
  background: var(--green);
  color: var(--background-primary);
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius-small);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-light);
}

.copy-btn:hover {
  background: var(--green-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.copy-btn:active {
  transform: translateY(0);
}

#xpathInput {
  width: 100%;
  height: 90px;
  background: var(--background-secondary);
  border: 1px solid var(--separator);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  padding: 12px 16px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

#xpathInput:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.25);
}

#xpathInput::placeholder {
  color: var(--text-tertiary);
}

.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: var(--background-primary);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.results-header span:first-child {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
}

.count {
  color: var(--text-tertiary);
  font-size: 13px;
  font-weight: 500;
  background: var(--background-secondary);
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
}

.copy-all-btn {
  background: var(--background-secondary);
  color: var(--primary-blue);
  border: 1px solid var(--separator);
  padding: 6px 12px;
  border-radius: var(--border-radius-small);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copy-all-btn:hover {
  background: var(--primary-blue);
  color: var(--background-primary);
  border-color: var(--primary-blue);
}

.copy-all-btn:active {
  transform: scale(0.98);
}

.results-container {
  flex: 1;
  background: var(--background-secondary);
  border: 1px solid var(--separator);
  border-radius: var(--border-radius);
  padding: 0;
  overflow: hidden;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 12px;
  box-shadow: var(--shadow-light);
}

.results-container:empty::before {
  content: "暂无结果";
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-tertiary);
  font-style: italic;
}

.result-item {
  padding: 12px 16px;
  border-bottom: 0.5px solid var(--separator);
  cursor: pointer;
  transition: all 0.15s ease;
  color: var(--text-secondary);
  line-height: 1.4;
  position: relative;
}

.result-item:hover {
  background: rgba(0, 122, 255, 0.1);
  color: var(--text-primary);
}

.result-item:active {
  background: rgba(0, 122, 255, 0.2);
}

.result-item:last-child {
  border-bottom: none;
}

.result-item::before {
  content: counter(result-counter);
  counter-increment: result-counter;
  position: absolute;
  left: 16px;
  top: 12px;
  background: var(--primary-blue);
  color: var(--background-primary);
  font-size: 10px;
  font-weight: 600;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.results-container {
  counter-reset: result-counter;
}

.result-item {
  padding-left: 44px;
}

/* Scrollbar styling for webkit browsers */
.results-container::-webkit-scrollbar {
  width: 6px;
}

.results-container::-webkit-scrollbar-track {
  background: transparent;
}

.results-container::-webkit-scrollbar-thumb {
  background: var(--gray-3);
  border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-2);
}

/* Animation for smooth transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-item {
  animation: fadeIn 0.3s ease;
}

/* Focus states for accessibility */
.copy-btn:focus,
.copy-all-btn:focus,
.close-btn:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

#xpathInput:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.25);
}