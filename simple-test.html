<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath扩展简单测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f7;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .test-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.2s ease;
        }
        
        .test-btn:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }
        
        .test-element {
            padding: 20px;
            margin: 15px 0;
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .test-element:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
        }
        
        .instructions {
            background: #f2f2f7;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        
        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            background: #007AFF;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 XPath扩展测试</h1>
        
        <div class="instructions">
            <h3>📋 测试步骤：</h3>
            <ol>
                <li><strong>显示侧边栏</strong>：点击下面的按钮</li>
                <li><strong>测试关闭按钮</strong>：点击侧边栏右上角的 × 按钮</li>
                <li><strong>测试Shift功能</strong>：重新显示侧边栏，按住Shift键并点击测试元素</li>
                <li><strong>测试Clear按钮</strong>：点击橙色的Clear按钮</li>
                <li><strong>测试Copy按钮</strong>：点击绿色的Copy按钮</li>
            </ol>
        </div>
        
        <div class="controls">
            <button class="test-btn" onclick="showSidebar()">显示侧边栏</button>
            <button class="test-btn" onclick="runDebugScript()">运行调试</button>
            <button class="test-btn" onclick="runForceFixScript()">强制修复</button>
        </div>
        
        <div class="test-elements">
            <div class="test-element" id="test-element-1">
                <h3>测试元素 1</h3>
                <p>这是一个带有ID的测试元素</p>
            </div>
            
            <div class="test-element" data-test="element-2">
                <h3>测试元素 2</h3>
                <p>这是一个带有data属性的测试元素</p>
            </div>
            
            <div class="test-element">
                <h3>测试元素 3</h3>
                <p>这是一个普通的测试元素</p>
                <span>包含嵌套元素</span>
            </div>
        </div>
    </div>
    
    <div class="status" id="status">等待测试开始</div>
    
    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.background = type === 'success' ? '#34C759' : 
                                    type === 'error' ? '#FF3B30' : '#007AFF';
        }
        
        function showSidebar() {
            if (typeof window.showSidebar === 'function') {
                window.showSidebar();
                updateStatus('侧边栏已显示', 'success');
            } else {
                updateStatus('XPath扩展未加载', 'error');
            }
        }
        
        function runDebugScript() {
            updateStatus('运行调试脚本...', 'info');
            // 这里会运行调试脚本的内容
            console.log('请在控制台中复制并运行 debug-current-state.js 的内容');
        }
        
        function runForceFixScript() {
            updateStatus('运行强制修复...', 'info');
            // 这里会运行强制修复脚本的内容
            console.log('请在控制台中复制并运行 force-fix.js 的内容');
        }
        
        // 检查扩展状态
        function checkExtensionStatus() {
            if (typeof window.toggleSidebar === 'function') {
                updateStatus('XPath扩展已加载', 'success');
            } else {
                updateStatus('等待XPath扩展加载...', 'info');
                setTimeout(checkExtensionStatus, 1000);
            }
        }
        
        // 页面加载后检查状态
        document.addEventListener('DOMContentLoaded', () => {
            checkExtensionStatus();
        });
        
        // 监听Shift键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift') {
                updateStatus('Shift键已按下', 'info');
            }
        });
        
        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                updateStatus('Shift键已释放', 'info');
            }
        });
    </script>
</body>
</html>
