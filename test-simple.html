<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
            line-height: 1.6;
            margin: 40px;
            background: #f5f5f7;
        }
        .test-element {
            padding: 20px;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007AFF;
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="status" id="status">等待扩展加载...</div>
    
    <h1>XPath测试器 - 简单测试页面</h1>
    
    <div class="test-element" id="test-1">
        <h2>测试元素 1</h2>
        <p>这是第一个测试元素，用于验证XPath生成。</p>
        <button>测试按钮</button>
    </div>
    
    <div class="test-element" class="special-class">
        <h2>测试元素 2</h2>
        <p>这是第二个测试元素，带有特殊class。</p>
        <ul>
            <li>列表项 1</li>
            <li>列表项 2</li>
            <li>列表项 3</li>
        </ul>
    </div>
    
    <div class="test-element" data-test="example">
        <h2>测试元素 3</h2>
        <p>这是第三个测试元素，带有data属性。</p>
        <span>内联元素</span>
    </div>

    <script>
        let statusEl = document.getElementById('status');
        
        function updateStatus(message, color = '#007AFF') {
            statusEl.textContent = message;
            statusEl.style.background = color;
            console.log('Status:', message);
        }
        
        // 检查扩展状态
        function checkExtensionStatus() {
            const sidebar = document.getElementById('xpath-sidebar');
            const initialized = window.xpathSidebarInitialized;
            const hasToggle = typeof window.toggleSidebar === 'function';
            
            if (sidebar && initialized && hasToggle) {
                updateStatus('✅ 扩展已加载', '#34C759');
            } else if (sidebar) {
                updateStatus('⚠️ 扩展部分加载', '#FF9500');
            } else {
                updateStatus('❌ 扩展未加载', '#FF3B30');
            }
            
            console.log('Extension status:', {
                sidebar: !!sidebar,
                initialized,
                hasToggle
            });
        }
        
        // 定期检查扩展状态
        setInterval(checkExtensionStatus, 1000);
        
        // 页面加载完成后检查
        window.addEventListener('load', () => {
            console.log('Page loaded');
            setTimeout(checkExtensionStatus, 500);
        });
        
        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            updateStatus('🎉 侧边栏已创建', '#34C759');
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
