// XPath扩展测试脚本
// 在浏览器控制台中运行此脚本来测试修复效果

(function() {
    console.log('🧪 开始测试XPath扩展修复效果...');
    
    // 测试1: 检查基本函数是否存在
    console.log('\n📋 测试1: 检查基本函数');
    const functions = [
        'toggleSidebar',
        'showSidebar', 
        'hideSidebar',
        'createSidebar',
        'highlightElement',
        'removeHighlight',
        'getXPath',
        'updateXPathInput',
        'executeXPath',
        'updateResults'
    ];
    
    functions.forEach(func => {
        const exists = typeof window[func] === 'function';
        console.log(`  ${func}: ${exists ? '✅' : '❌'}`);
    });
    
    // 测试2: 检查全局变量
    console.log('\n📋 测试2: 检查全局变量');
    const variables = [
        'xpathSidebarInitialized',
        'sidebarVisible',
        'sidebar',
        'currentOverlay'
    ];
    
    variables.forEach(variable => {
        const exists = window.hasOwnProperty(variable);
        console.log(`  ${variable}: ${exists ? '✅' : '❌'} (${typeof window[variable]})`);
    });
    
    // 测试3: 检查CSS样式
    console.log('\n📋 测试3: 检查CSS样式');
    const testElement = document.createElement('div');
    testElement.className = 'xpath-highlight-overlay';
    document.body.appendChild(testElement);
    
    const styles = window.getComputedStyle(testElement);
    const hasStyles = styles.position === 'fixed' && styles.zIndex;
    console.log(`  高亮样式: ${hasStyles ? '✅' : '❌'}`);
    
    document.body.removeChild(testElement);
    
    // 测试4: 模拟Shift+点击
    console.log('\n📋 测试4: 模拟功能测试');
    
    if (typeof window.showSidebar === 'function') {
        console.log('  显示侧边栏...');
        window.showSidebar();
        
        setTimeout(() => {
            const sidebar = document.getElementById('xpath-sidebar');
            console.log(`  侧边栏创建: ${sidebar ? '✅' : '❌'}`);
            
            if (sidebar) {
                // 测试高亮功能
                if (typeof window.highlightElement === 'function') {
                    console.log('  测试高亮功能...');
                    window.highlightElement(document.body);
                    
                    setTimeout(() => {
                        const overlay = document.querySelector('.xpath-highlight-overlay');
                        console.log(`  高亮覆盖层: ${overlay ? '✅' : '❌'}`);
                        
                        // 测试XPath生成
                        if (typeof window.getXPath === 'function') {
                            const xpath = window.getXPath(document.body);
                            console.log(`  XPath生成: ${xpath ? '✅' : '❌'} (${xpath})`);
                            
                            // 测试XPath执行
                            if (typeof window.updateXPathInput === 'function') {
                                window.updateXPathInput(xpath);
                                
                                setTimeout(() => {
                                    const input = document.getElementById('xpath-input');
                                    const hasValue = input && input.value === xpath;
                                    console.log(`  XPath输入更新: ${hasValue ? '✅' : '❌'}`);
                                    
                                    const resultsContainer = document.getElementById('xpath-results-container');
                                    const hasResults = resultsContainer && resultsContainer.children.length > 0;
                                    console.log(`  结果显示: ${hasResults ? '✅' : '❌'}`);
                                    
                                    console.log('\n🎉 测试完成！');
                                    console.log('如果所有项目都显示✅，说明修复成功。');
                                    console.log('现在可以尝试按住Shift键并点击页面元素。');
                                }, 100);
                            }
                        }
                    }, 100);
                }
            }
        }, 100);
    } else {
        console.log('❌ showSidebar函数不存在，扩展可能未正确加载');
    }
})();
