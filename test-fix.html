<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XPath选择器修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f7;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1d1d1f;
            margin-bottom: 20px;
        }

        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #d2d2d7;
            border-radius: 8px;
            background: #f2f2f7;
        }

        .test-element {
            padding: 15px;
            margin: 10px 0;
            background: #007AFF;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-element:hover {
            background: #0056CC;
            transform: translateY(-2px);
        }

        .instructions {
            background: linear-gradient(135deg, #007AFF, #5AC8FA);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            z-index: 1000;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }

        .status.success {
            background: #34C759;
            color: white;
        }

        .status.error {
            background: #FF3B30;
            color: white;
        }

        .status.info {
            background: #007AFF;
            color: white;
        }

        .log {
            background: #1c1c1e;
            color: #ffffff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }

        .toggle-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.2s ease;
        }

        .toggle-btn:hover {
            background: #0056CC;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="instructions">
            <h2>🧪 XPath选择器修复测试</h2>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>点击"显示侧边栏"按钮（侧边栏宽度应为35%）</li>
                <li>测试关闭按钮（点击右上角×按钮）</li>
                <li>重新显示侧边栏，测试Clear按钮（橙色按钮）</li>
                <li>按住 <kbd>Shift</kbd> 键</li>
                <li>将鼠标悬停在下面的测试元素上（应该看到单个蓝色高亮框）</li>
                <li>按住 <kbd>Shift</kbd> 键点击测试元素（应该生成XPath并显示结果）</li>
                <li>测试Copy按钮（绿色按钮）</li>
            </ol>
        </div>

        <div class="controls">
            <button class="toggle-btn" onclick="toggleSidebar()">显示侧边栏</button>
            <button class="toggle-btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>测试元素区域</h3>
            <div class="test-element" id="test-1">
                <h4>测试元素 1</h4>
                <p>这是第一个测试元素，包含ID属性</p>
            </div>

            <div class="test-element" data-test="element-2">
                <h4>测试元素 2</h4>
                <p>这是第二个测试元素，包含data属性</p>
            </div>

            <div class="test-element">
                <h4>测试元素 3</h4>
                <p>这是第三个测试元素，只有class属性</p>
                <span>嵌套的span元素</span>
            </div>
        </div>

        <div class="log" id="log">
            <div class="log-entry">📋 测试日志将在这里显示...</div>
        </div>
    </div>

    <div class="status info" id="status">等待开始测试</div>

    <script>
        // 日志功能
        function log(message) {
            const logContainer = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div class="log-entry">📋 日志已清空</div>';
        }

        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 侧边栏控制
        function toggleSidebar() {
            if (typeof window.toggleSidebar === 'function') {
                window.toggleSidebar();
                log('🔄 调用 window.toggleSidebar()');
            } else {
                log('❌ window.toggleSidebar 函数不存在');
                updateStatus('XPath插件未加载', 'error');
            }
        }

        // 检查插件状态
        function checkPluginStatus() {
            const status = {
                initialized: !!window.xpathSidebarInitialized,
                hasToggle: typeof window.toggleSidebar === 'function',
                hasShow: typeof window.showSidebar === 'function',
                hasHighlight: typeof window.highlightElement === 'function',
                hasSidebar: !!window.sidebar,
                sidebarVisible: !!window.sidebarVisible
            };

            log('📊 插件状态检查:');
            Object.entries(status).forEach(([key, value]) => {
                log(`  ${key}: ${value ? '✅' : '❌'}`);
            });

            if (status.initialized && status.hasToggle) {
                updateStatus('XPath插件已就绪', 'success');
            } else {
                updateStatus('XPath插件未完全加载', 'error');
            }
        }

        // 监听插件加载
        let checkCount = 0;
        const maxChecks = 10;

        function waitForPlugin() {
            checkCount++;
            if (window.xpathSidebarInitialized) {
                log('🎉 XPath插件已加载！');
                checkPluginStatus();
                return;
            }

            if (checkCount < maxChecks) {
                log(`⏳ 等待插件加载... (${checkCount}/${maxChecks})`);
                setTimeout(waitForPlugin, 500);
            } else {
                log('❌ 插件加载超时');
                updateStatus('插件加载失败', 'error');
            }
        }

        // 页面加载完成后开始检查
        document.addEventListener('DOMContentLoaded', () => {
            log('📄 页面加载完成');
            waitForPlugin();
        });

        // 监听Shift键状态
        let shiftPressed = false;

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Shift' && !shiftPressed) {
                shiftPressed = true;
                log('⌨️ Shift键已按下');
                updateStatus('Shift模式激活', 'info');
            }
        });

        document.addEventListener('keyup', (e) => {
            if (e.key === 'Shift') {
                shiftPressed = false;
                log('⌨️ Shift键已释放');
                updateStatus('Shift模式关闭', 'info');
            }
        });

        // 监听鼠标事件
        document.addEventListener('mouseover', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                log(`🖱️ Shift+悬停: ${e.target.tagName} (${e.target.textContent.substring(0, 30)}...)`);
            }
        });

        document.addEventListener('click', (e) => {
            if (shiftPressed && e.target.closest('.test-element')) {
                const element = e.target.closest('.test-element');
                log(`🎯 Shift+点击: ${element.querySelector('h4').textContent}`);

                // 检查XPath是否生成
                setTimeout(() => {
                    const xpathInput = document.getElementById('xpath-input');
                    if (xpathInput && xpathInput.value) {
                        log(`✅ XPath已生成: ${xpathInput.value}`);
                        updateStatus('XPath生成成功！', 'success');
                    } else {
                        log('❌ XPath未生成');
                        updateStatus('XPath生成失败', 'error');
                    }
                }, 100);
            }
        });

        // 监听DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.id === 'xpath-sidebar') {
                            log('🎉 检测到侧边栏被创建！');
                            updateStatus('侧边栏已显示', 'success');
                        }
                        if (node.className === 'xpath-highlight-overlay') {
                            log('🎯 检测到高亮覆盖层被创建！');
                        }
                    });
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
