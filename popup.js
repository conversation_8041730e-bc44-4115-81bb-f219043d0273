document.addEventListener('DOMContentLoaded', function() {
  const xpathInput = document.getElementById('xpathInput');
  const copyXPathBtn = document.getElementById('copyXPath');
  const copyAllBtn = document.getElementById('copyAll');
  const closeBtn = document.getElementById('closeBtn');
  const resultCount = document.getElementById('resultCount');
  const resultsContainer = document.getElementById('resultsContainer');

  let currentResults = [];

  // XPath输入变化时执行查询
  xpathInput.addEventListener('input', debounce(executeXPath, 300));

  // 复制XPath
  copyXPathBtn.addEventListener('click', async () => {
    try {
      await navigator.clipboard.writeText(xpathInput.value);
      showFeedback(copyXPathBtn, 'Copied!');
    } catch (err) {
      console.error('Failed to copy XPath:', err);
    }
  });

  // 复制所有结果
  copyAllBtn.addEventListener('click', async () => {
    try {
      const allText = currentResults.map(r => r.text).join('\n');
      await navigator.clipboard.writeText(allText);
      showFeedback(copyAllBtn, 'Copied!');
    } catch (err) {
      console.error('Failed to copy results:', err);
    }
  });

  // 关闭弹窗
  closeBtn.addEventListener('click', () => {
    window.close();
  });

  function executeXPath() {
    const xpath = xpathInput.value.trim();
    if (!xpath) {
      updateResults([]);
      return;
    }

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'executeXPath',
        xpath: xpath
      }, function(response) {
        if (response && response.results) {
          updateResults(response.results);
        } else {
          updateResults([]);
        }
      });
    });
  }

  function updateResults(results) {
    currentResults = results;
    resultCount.textContent = `${results.length} items`;

    resultsContainer.innerHTML = '';

    results.forEach((result) => {
      const item = document.createElement('div');
      item.className = 'result-item';
      item.textContent = result.text || '[空文本]';
      item.title = `标签: ${result.tagName}\n文本: ${result.text}`;

      item.addEventListener('click', async () => {
        try {
          await navigator.clipboard.writeText(result.text);
          showFeedback(item, 'Copied!');
        } catch (err) {
          console.error('Failed to copy result:', err);
        }
      });

      resultsContainer.appendChild(item);
    });
  }

  function showFeedback(element, message) {
    const originalText = element.textContent;
    element.textContent = message;
    element.style.background = 'var(--green)';
    element.style.color = 'var(--background-primary)';

    setTimeout(() => {
      element.textContent = originalText;
      element.style.background = '';
      element.style.color = '';
    }, 1000);
  }

  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // 监听来自content script的消息
  chrome.runtime.onMessage.addListener((request) => {
    if (request.action === 'updateXPath') {
      xpathInput.value = request.xpath;
      executeXPath();
    }
  });
});